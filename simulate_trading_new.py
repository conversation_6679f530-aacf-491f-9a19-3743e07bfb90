import math
import os
import argparse
import logging
from pathlib import Path
from datetime import datetime, timedelta, timezone
import sys
import yaml
import json
import pandas as pd
import numpy as np
import pyarrow.parquet as pq
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import torch
import random
import hashlib
from stable_baselines3 import SAC  # still allow fallback, but not used for PopArt checkpoints
from stable_baselines3.common.vec_env import VecNormalize
from popart_sac import PopArtSAC    # <--- CUSTOM class to load PopArt models
import itertools
from copy import deepcopy

# Import new adaptive signal processing modules
from signal_smoothing import SignalSmoother
from adaptive_thresholds import AdaptiveThresholdTuner, create_threshold_tuner_from_config

# Import the prepare_df function from agent.py
def _dt(s: str) -> datetime:
    return datetime.fromisoformat(s.replace("Z", "+00:00")).astimezone(timezone.utc)

def safe_read_parquet(file_path, **kwargs):
    """
    Safe wrapper around pd.read_parquet that validates data quality.
    Detects and handles corrupted market data with 0.00000 prices.
    """
    try:
        df = pd.read_parquet(file_path, **kwargs)
        
        # Check if this is market OHLC data
        ohlc_cols = ['open', 'high', 'low', 'close']
        available_ohlc = [col for col in ohlc_cols if col in df.columns]
        
        if available_ohlc:
            # Check for corrupted 0.00000 prices in OHLC data
            for col in available_ohlc:
                zero_mask = (df[col] == 0.0) | (df[col].isna())
                if zero_mask.any():
                    corrupted_count = zero_mask.sum()
                    total_rows = len(df)
                    
                    if corrupted_count > 0:
                        log.warning(f"⚠️ DATA CORRUPTION DETECTED in {file_path}")
                        log.warning(f"   Column '{col}' has {corrupted_count}/{total_rows} corrupted (0.00000) values")
                        
                        # Show first few corrupted timestamps for debugging
                        if 'timestamp' in df.columns:
                            corrupted_rows = df[zero_mask]
                            for idx, row in corrupted_rows.head(3).iterrows():
                                timestamp = pd.to_datetime(row['timestamp'], utc=True) if pd.notna(row['timestamp']) else 'Unknown'
                                ohlc_vals = {c: row.get(c, 'N/A') for c in available_ohlc}
                                log.warning(f"   {timestamp}: {ohlc_vals}")
                        
                        # CRITICAL: Remove corrupted rows to prevent trading losses
                        # Only remove rows where ANY OHLC value is 0.0 or NaN
                        corruption_mask = pd.Series(False, index=df.index)
                        for ohlc_col in available_ohlc:
                            corruption_mask |= (df[ohlc_col] == 0.0) | (df[ohlc_col].isna())
                        
                        if corruption_mask.any():
                            clean_df = df[~corruption_mask].copy()
                            removed_count = len(df) - len(clean_df)
                            log.warning(f"   🧹 CLEANED: Removed {removed_count} corrupted rows")
                            log.warning(f"   ✅ SAFE DATA: {len(clean_df)} clean rows remaining")
                            df = clean_df
                        
                        # If too much data is corrupted, raise error
                        if len(df) < total_rows * 0.5:  # More than 50% corrupted
                            raise ValueError(f"File {file_path} has too much corrupted data ({corrupted_count}/{total_rows} rows). Cannot proceed safely.")
        
        return df
        
    except Exception as e:
        log.error(f"Error reading {file_path}: {e}")
        raise

def prepare_df_for_simulation(cfg, start, end):
    """Same logic as in agent.py"""
    from joblib import Parallel, delayed
    
    symbol = cfg["symbol"]
    tf = cfg["primaryTimeframe"]
    
    root = Path(os.environ.get("PARQUET_DIR", cfg.get("featureParquetDir", "parquet"))).expanduser()
    tf_dir = next((d for d in (root/symbol/tf, root/symbol/"ohlcv"/tf) if d.exists()), None)
    if tf_dir is None:
        raise FileNotFoundError(f"No parquet dir under {root}")
    
    warm = start - timedelta(hours=24)
    
    def load_single_day(fp):
        if fp.exists():
            df = safe_read_parquet(fp, engine="pyarrow")
            if "timestamp" in df.columns:
                df.set_index("timestamp", inplace=True)
            df.index = pd.to_datetime(df.index, utc=True)
            return df
        return None
    
    file_paths = []
    cur = warm.date()
    while cur <= end.date():
        file_paths.append(tf_dir / f"{cur}.parquet")
        cur += timedelta(days=1)
    
    dfs = Parallel(n_jobs=8)(delayed(load_single_day)(fp) for fp in file_paths)
    
    dfs_filtered = [df for df in dfs if df is not None]
    if not dfs_filtered:
        raise RuntimeError("no parquet files in range")
    
    df_all = pd.concat(dfs_filtered).sort_index().loc[warm:end]
    feats = cfg["envSettings"]["feature_columns"]
    
    # CRITICAL: Always use features from config, never from df_all.columns
    # Make a deep copy to prevent any reference issues
    EXPECTED_FEATURE_COLS = list(feats)  # Deep copy

    missing = [c for c in EXPECTED_FEATURE_COLS if c not in df_all.columns]
    if missing:
        log.warning("Missing %d columns → filled with zeros: %s", len(missing), ", ".join(missing))
        df_all = df_all.copy()
        for col in missing:
            df_all[col] = 0.0

    # ●●●●●●●● ORDER-BOOK MASKING CONFIGURATION ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
    # Check if order-book masking is enabled in configuration
    trade_params = config.get('tradeParams', {})
    mask_orderbook = trade_params.get('maskOrderBook', False)
    masking_ratio = trade_params.get('orderBookMaskingRatio', 0.5)

    if mask_orderbook:
        log.info(f"📊 ORDER-BOOK MASKING ENABLED: Masking {masking_ratio*100:.1f}% of order-book features")

        # Define order-book columns to mask
        orderbook_cols = [col for col in EXPECTED_FEATURE_COLS if
                         col.startswith('ob_') or col.startswith('dvol_') or
                         col in ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5']]

        log.info(f"   Order-book columns to mask: {orderbook_cols}")

        # Apply masking by setting random percentage of values to median
        np.random.seed(42)  # Deterministic masking
        for col in orderbook_cols:
            if col in df_all.columns:
                col_median = df_all[col].median()
                mask_indices = np.random.choice(len(df_all), size=int(len(df_all) * masking_ratio), replace=False)
                df_all.loc[df_all.index[mask_indices], col] = col_median

        log.info(f"   ✅ Masked {len(orderbook_cols)} order-book columns with ratio {masking_ratio}")
    else:
        log.info("📊 ORDER-BOOK MASKING DISABLED: Using original order-book data")

    # Apply forward fill (exactly like live_trading.py)
    df_all = df_all.copy()
    df_all[EXPECTED_FEATURE_COLS] = df_all[EXPECTED_FEATURE_COLS].ffill()

    # Apply log1p transformation to volume columns (exactly like live_trading.py)
    vol_cols = [c for c in EXPECTED_FEATURE_COLS if "vol" in c]
    if vol_cols:
        # Safe log1p transformation - handle zero/negative values
        for col in vol_cols:
            if col in df_all.columns:
                # More robust NaN handling
                df_all[col] = df_all[col].fillna(1e-8)
                df_all[col] = np.where(df_all[col] <= 0, 1e-8, df_all[col])
        df_all[vol_cols] = np.log1p(df_all[vol_cols])
        log.debug(f"Applied log1p to volume columns: {vol_cols}")

    # Replace infinities with NaN (exactly like live_trading.py)
    df_all.replace([np.inf, -np.inf], np.nan, inplace=True)

    # More selective NaN filtering - only check essential OHLCV features
    essential_features = [col for col in ['open', 'high', 'low', 'close', 'volume'] if col in EXPECTED_FEATURE_COLS]
    if essential_features:
        # Filter out rows with NaN in essential features only
        df_all = df_all[np.isfinite(df_all[essential_features]).all(axis=1)]
        log.info(f"✅ Filtered by essential features: {len(df_all)} rows remaining")

    # Fill remaining NaN values in non-essential features with 0
    remaining_features = [col for col in EXPECTED_FEATURE_COLS if col not in essential_features]
    if remaining_features:
        nan_counts_before = df_all[remaining_features].isna().sum()
        if nan_counts_before.sum() > 0:
            log.info(f"📊 Filling NaN in non-essential features: {nan_counts_before[nan_counts_before > 0].to_dict()}")
            # Fill NaN values for all remaining features
            for col in remaining_features:
                df_all[col] = df_all[col].fillna(0.0)

    return df_all.loc[start:end, EXPECTED_FEATURE_COLS].astype("float32")

custom_objects = {
    # 1️⃣  buffer – prebijeme triedu a vypneme "timeout termination",
    "replay_buffer_kwargs": dict(handle_timeout_termination=False),

    # 2️⃣  SB3 loaderu povieme, že optimalizácia pamäte netreba
    "optimize_memory_usage": False,

    # 3️⃣  picklené λ-funkcie zahoď a nahraď konštantou/None
    "learning_rate": 0.0,        # scalar namiesto lambda
    "lr_schedule":   None,
}

# Pokus o import gymnasium alebo gym pre prístup k spaces.Box
try:
    import gymnasium as gym_spaces
    log_gym_lib = "gymnasium"
except ImportError:
    try:
        import gym as gym_spaces
        log_gym_lib = "gym"
    except ImportError:
        print("WARNING: Ani 'gymnasium' ani 'gym' knižnica nenájdená. Budú použité predvolené hranice [-5, 5].")
        gym_spaces = None
        log_gym_lib = "None"

# ●●●●●●●● ENHANCED LOGGING SETUP (exactly like live_trading.py) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
logging.basicConfig(
    level   = logging.INFO,
    format  = "%(asctime)s [%(name)s] [%(levelname)s] %(message)s",
    datefmt = "%Y-%m-%d %H:%M:%S",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("simulate_trading.log"),  # Main simulation log
        logging.FileHandler("indicators_thresholds.log")  # Detailed indicators & thresholds log
    ]
)
log = logging.getLogger("SimulateTrading")

# Create separate logger for detailed indicators & thresholds
indicators_log = logging.getLogger("IndicatorsThresholds")
indicators_log.setLevel(logging.INFO)
# Create file handler specifically for indicators & thresholds
indicators_handler = logging.FileHandler("indicators_thresholds.log")
indicators_handler.setFormatter(logging.Formatter("%(asctime)s [%(levelname)s] %(message)s", "%Y-%m-%d %H:%M:%S"))
indicators_log.addHandler(indicators_handler)
indicators_log.propagate = False  # Prevent duplicate logs in main logger

# --- Pomocné funkcie ---
def load_config(config_path: Path) -> dict:
    """Načíta konfiguračný súbor YAML/JSON."""
    log.info(f"Načítavam konfiguráciu z: {config_path}")
    try:
        with open(config_path, 'r') as f:
            if config_path.suffix.lower() == '.json': config = json.load(f)
            elif config_path.suffix.lower() in ['.yaml', '.yml']: config = yaml.safe_load(f)
            else: raise ValueError(f"Nepodporovaný formát config súboru: {config_path.suffix}")
    except Exception as e: log.error(f"Chyba pri načítaní/spracovaní configu {config_path}: {e}"); raise

    try:
        # Pridanie .zip k ceste modelu, ak tam ešte nie je a cesta existuje
        model_path_str = config['trainingSettings']['modelSavePath']
        if not model_path_str.endswith(".zip"): model_path_str += ".zip"

        config['runtime'] = {
            'data_dir': Path(config['featureParquetDir']),
            'model_path': Path(model_path_str)
        }
    except KeyError as e: raise ValueError(f"Chýbajúci kľúč v konfigurácii potrebný pre runtime cesty: {e}")

    log.info(f"Konfigurácia načítaná pre symbol: {config.get('symbol', 'N/A')}")
    return config

def load_backtest_data(config: dict, start_date: datetime, end_date: datetime, load_second_data: bool = True, use_1s_decisions: bool = False) -> dict:
    """
    Načíta a spojí denné Parquet súbory pre backtest obdobie.
    Načíta aj skoršie dáta na "zahriatie" indikátorov s dlhým lookbackom.
    Ak load_second_data=True, načíta aj 1-sekundové dáta pre presnejšie simulácie SL/TP.
    Ak use_1s_decisions=True, pripraví dáta pre 1s rozhodovanie s 5m features.
    Vracia slovník s 'primary' (5m/1s) a voliteľne 'second' (1s) dataframe.
    """
    symbol = config.get('symbol')
    tf_cfg = config.get('primaryTimeframe')
    # Ak je tf zoznam → použi prvý (hlavný) timeframe
    timeframe = tf_cfg[0] if isinstance(tf_cfg, (list, tuple)) else tf_cfg
    data_dir = config.get('runtime', {}).get('data_dir')
    if not all([symbol, timeframe, data_dir]):
        raise ValueError("Chýbajúce kľúče 'symbol'/'primaryTimeframe'/'runtime.data_dir'.")

    # --- Výpočet maximálneho lookbacku pre zahriatie ---
    max_lookback_periods = 0
    # Základný lookback pre stav agenta
    state_lookback = config.get('envSettings', {}).get('state_lookback', 0)
    max_lookback_periods = max(max_lookback_periods, state_lookback)

    # Lookbacky z indikátorov (pridajte ďalšie podľa potreby)
    indicator_settings = config.get('indicatorSettings', {})
    if indicator_settings.get('hmm_5m', {}).get('enabled'):
        max_lookback_periods = max(max_lookback_periods, indicator_settings['hmm_5m'].get('window', 0))
    if indicator_settings.get('bollinger_5m', {}).get('enabled'):
        max_lookback_periods = max(max_lookback_periods, indicator_settings['bollinger_5m'].get('basePeriod', 0))
    if indicator_settings.get('atr_5m', {}).get('enabled'):
        max_lookback_periods = max(max_lookback_periods, indicator_settings['atr_5m'].get('basePeriod', 0))
    if indicator_settings.get('rsi_5m', {}).get('enabled'):
        max_lookback_periods = max(max_lookback_periods, indicator_settings['rsi_5m'].get('basePeriod', 0))
    if indicator_settings.get('adx', {}).get('enabled'):
        # ADX často potrebuje viac ako basePeriod kvôli vyhladzovaniu
        max_lookback_periods = max(max_lookback_periods, indicator_settings['adx'].get('basePeriod', 0) * 2)
    if indicator_settings.get('ema_5m', {}).get('enabled'):
        max_lookback_periods = max(max_lookback_periods, indicator_settings['ema_5m'].get('baseSlowPeriod', 0))
        max_lookback_periods = max(max_lookback_periods, indicator_settings['ema_5m'].get('baseFastPeriod', 0))
    # ... pridajte ďalšie kontroly pre vaše indikátory ...

    # Pridajme malý buffer pre istotu
    required_lookback_periods = max_lookback_periods + 10 # Napr. +10 periód buffer
    log.info(f"Maximálny potrebný lookback pre indikátory (vrátane buffera): {required_lookback_periods} periód")

    # --- Výpočet počtu dní na načítanie navyše ---
    try:
        tf_value = int(timeframe[:-1]) # Získa číslo z timeframe stringu, napr. 5 z '5m'
        tf_unit = timeframe[-1].lower() # Získa jednotku, napr. 'm' z '5m'
        if tf_unit == 'm': periods_per_day = 24 * 60 / tf_value
        elif tf_unit == 'h': periods_per_day = 24 / tf_value
        elif tf_unit == 'd': periods_per_day = 1
        else: raise ValueError(f"Nepodporovaná jednotka timeframe: {tf_unit}")

        # Odhad dní potrebných pre lookback + buffer (zaokrúhlené nahor)
        days_to_load_extra = math.ceil(required_lookback_periods / periods_per_day) + 2 # +2 dni buffer pre istotu/víkendy
    except Exception as e:
        log.warning(f"Nepodarilo sa presne určiť počet dní pre lookback z timeframe '{timeframe}': {e}. Používam fixný počet 5 dní navyše.")
        days_to_load_extra = 5 # Bezpečná predvolená hodnota

    load_start_date = start_date - timedelta(days=days_to_load_extra)
    log.info(f"Rozšírené obdobie načítavania dát pre zahriatie indikátorov: {load_start_date.date()} -> {end_date.date()}")

    # --- Načítanie primárnych dát (5m) z rozšíreného obdobia ---
    symbol_tf_dir = data_dir / symbol / timeframe
    all_files = []
    current_date = load_start_date
    while current_date <= end_date:
        day_file = symbol_tf_dir / f"{current_date.date()}.parquet"
        if day_file.exists():
            all_files.append(day_file)
        else:
            # Logujeme varovanie iba ak ide o dni v rámci *pôvodného* backtest rozsahu
            if current_date >= start_date:
                log.warning(f"Súbor pre {current_date.date()} neexistuje: {day_file}")
        current_date += timedelta(days=1)

    if not all_files:
        raise FileNotFoundError(f"Žiadne Parquet súbory pre symbol/timeframe {symbol}/{timeframe} v adresári {symbol_tf_dir} pre rozšírené obdobie.")
    log.info(f"Našlo sa {len(all_files)} súborov v rozšírenom období.")

    primary_dfs = []
    for f in all_files:
        try:
            primary_dfs.append(safe_read_parquet(f))
        except Exception as e:
            log.error(f"Chyba pri načítavaní {f}: {e}")
            raise
    if not primary_dfs:
        raise ValueError("Nepodarilo sa načítať žiadne dáta z Parquet súborov.")

    primary_df = pd.concat(primary_dfs, ignore_index=True)
    log.info(f"Spojených {len(primary_df)} riadkov z rozšíreného obdobia (5m).")

    # --- Načítanie sekundárnych dát (1s) ak je požadované alebo pre 1s decisions ---
    second_df = None
    if load_second_data or use_1s_decisions:
        second_timeframe = "1s"
        symbol_second_dir = data_dir / symbol / second_timeframe
        if not symbol_second_dir.exists():
            if use_1s_decisions:
                raise FileNotFoundError(f"1s dáta sú potrebné pre 1s rozhodovanie, ale adresár neexistuje: {symbol_second_dir}")
            log.warning(f"Adresár pre 1s dáta neexistuje: {symbol_second_dir}. Použijeme len 5m dáta.")
        else:
            second_files = []
            # Pre 1s decisions potrebujeme zahriatie aj v 1s dátach
            load_start_for_1s = load_start_date if use_1s_decisions else start_date
            current_date = load_start_for_1s
            while current_date <= end_date:
                day_file = symbol_second_dir / f"{current_date.date()}.parquet"
                if day_file.exists():
                    second_files.append(day_file)
                current_date += timedelta(days=1)

            if second_files:
                log.info(f"Našlo sa {len(second_files)} súborov 1s dát.")
                second_dfs = []
                for f in second_files:
                    try:
                        second_dfs.append(safe_read_parquet(f))
                    except Exception as e:
                        if use_1s_decisions:
                            raise RuntimeError(f"Chyba pri načítavaní kritických 1s dát {f}: {e}")
                        log.warning(f"Chyba pri načítavaní 1s dát {f}: {e}. Preskakujem.")

                if second_dfs:
                    second_df = pd.concat(second_dfs, ignore_index=True)
                    log.info(f"Spojených {len(second_df)} riadkov 1s dát.")

                    # Spracovanie timestamp pre 1s dáta
                    if 'timestamp' not in second_df.columns:
                        if use_1s_decisions:
                            raise ValueError("Chýba stĺpec 'timestamp' v 1s dátach potrebných pre 1s rozhodovanie.")
                        log.warning("Chýba stĺpec 'timestamp' v 1s dátach. Použijeme len 5m dáta.")
                        second_df = None
                    else:
                        # Konverzia na datetime a nastavenie ako index
                        if not isinstance(second_df.index, pd.DatetimeIndex):
                            second_df['timestamp'] = pd.to_datetime(second_df['timestamp'], utc=True)
                            second_df = second_df.sort_values('timestamp').set_index('timestamp')
                        else:
                            # Zabezpečíme, že index je UTC a zoradený
                            if second_df.index.tz is None:
                                second_df.index = second_df.index.tz_localize("UTC")
                            elif second_df.index.tz != timezone.utc:
                                second_df.index = second_df.index.tz_convert("UTC")
                            second_df = second_df.sort_index()

                        # Kontrola, či máme potrebné stĺpce pre SL/TP v 1s dátach
                        required_cols = ['open', 'high', 'low', 'close']
                        missing_cols = [col for col in required_cols if col not in second_df.columns]
                        if missing_cols:
                            if use_1s_decisions:
                                raise ValueError(f"V 1s dátach chýbajú potrebné stĺpce pre 1s rozhodovanie: {missing_cols}")
                            log.warning(f"V 1s dátach chýbajú potrebné stĺpce: {missing_cols}. Použijeme len 5m dáta.")
                            second_df = None
                elif use_1s_decisions:
                    raise RuntimeError("Nepodarilo sa načítať žiadne 1s dáta potrebné pre 1s rozhodovanie.")

    # --- Spracovanie Timestamp a zoradenie ---
    if 'timestamp' not in primary_df.columns:
        raise ValueError("Chýba stĺpec 'timestamp' v načítaných dátach")
    # Konverzia na datetime a nastavenie ako index (ak ešte nie je)
    if not isinstance(primary_df.index, pd.DatetimeIndex):
         primary_df['timestamp'] = pd.to_datetime(primary_df['timestamp'], utc=True)
         primary_df = primary_df.sort_values('timestamp').set_index('timestamp')
    else:
        # Zabezpečíme, že index je UTC a zoradený
        if primary_df.index.tz is None:
            primary_df.index = primary_df.index.tz_localize("UTC")
        elif primary_df.index.tz != timezone.utc:
            primary_df.index = primary_df.index.tz_convert("UTC")
        primary_df = primary_df.sort_index()

    # --- Kontrola Features a definovanie Essential Columns ---
    try:
        feature_cols = config['envSettings']['feature_columns']
        assert isinstance(feature_cols, list)
    except (KeyError, AssertionError):
        raise ValueError("Chýbajúci alebo neplatný 'envSettings.feature_columns' v konfigurácii.")
    log.info(f"Očakávaných {len(feature_cols)} features podľa configu.")

    # CRITICAL: Always use features from config, never from primary_df.columns
    # Make a deep copy to prevent any reference issues
    EXPECTED_FEATURE_COLS = list(feature_cols)  # Deep copy

    missing_cols = [col for col in EXPECTED_FEATURE_COLS if col not in primary_df.columns]
    if missing_cols:
        log.warning(f"Missing {len(missing_cols)} columns → filled with zeros: {', '.join(missing_cols)}")
        primary_df = primary_df.copy()
        for col in missing_cols:
            primary_df[col] = 0.0

    # ●●●●●●●● ORDER-BOOK MASKING CONFIGURATION ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
    # Check if order-book masking is enabled in configuration
    trade_params = config.get('tradeParams', {})
    mask_orderbook = trade_params.get('maskOrderBook', False)
    masking_ratio = trade_params.get('orderBookMaskingRatio', 0.5)

    if mask_orderbook:
        log.info(f"📊 ORDER-BOOK MASKING ENABLED: Masking {masking_ratio*100:.1f}% of order-book features")

        # Define order-book columns to mask
        orderbook_cols = [col for col in EXPECTED_FEATURE_COLS if
                         col.startswith('ob_') or col.startswith('dvol_') or
                         col in ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5']]

        log.info(f"   Order-book columns to mask: {orderbook_cols}")

        # Apply masking by setting random percentage of values to median
        np.random.seed(42)  # Deterministic masking
        for col in orderbook_cols:
            if col in primary_df.columns:
                col_median = primary_df[col].median()
                mask_indices = np.random.choice(len(primary_df), size=int(len(primary_df) * masking_ratio), replace=False)
                primary_df.loc[primary_df.index[mask_indices], col] = col_median

        log.info(f"   ✅ Masked {len(orderbook_cols)} order-book columns with ratio {masking_ratio}")
    else:
        log.info("📊 ORDER-BOOK MASKING DISABLED: Using original order-book data")

    # Apply forward fill (exactly like live_trading.py)
    primary_df = primary_df.copy()
    primary_df[EXPECTED_FEATURE_COLS] = primary_df[EXPECTED_FEATURE_COLS].ffill()

    # Apply log1p transformation to volume columns (exactly like live_trading.py)
    vol_cols = [c for c in EXPECTED_FEATURE_COLS if "vol" in c]
    if vol_cols:
        # Safe log1p transformation - handle zero/negative values
        for col in vol_cols:
            if col in primary_df.columns:
                # More robust NaN handling
                primary_df[col] = primary_df[col].fillna(1e-8)
                primary_df[col] = np.where(primary_df[col] <= 0, 1e-8, primary_df[col])
        primary_df[vol_cols] = np.log1p(primary_df[vol_cols])
        log.debug(f"Applied log1p to volume columns: {vol_cols}")

    # Replace infinities with NaN (exactly like live_trading.py)
    primary_df.replace([np.inf, -np.inf], np.nan, inplace=True)

    # More selective NaN filtering - only check essential OHLCV features
    essential_features = [col for col in ['open', 'high', 'low', 'close', 'volume'] if col in EXPECTED_FEATURE_COLS]
    if essential_features:
        # Check NaN status before filtering
        nan_counts = primary_df[essential_features].isna().sum()
        if nan_counts.sum() > 0:
            log.warning(f"⚠️ NaN values in essential features before filtering: {nan_counts.to_dict()}")

        # Filter out rows with NaN in essential features only
        primary_df = primary_df[np.isfinite(primary_df[essential_features]).all(axis=1)]
        log.info(f"✅ Filtered by essential features: {len(primary_df)} rows remaining")

    # Fill remaining NaN values in non-essential features with 0
    remaining_features = [col for col in EXPECTED_FEATURE_COLS if col not in essential_features]
    if remaining_features:
        nan_counts_before = primary_df[remaining_features].isna().sum()
        if nan_counts_before.sum() > 0:
            log.info(f"📊 Filling NaN in non-essential features: {nan_counts_before[nan_counts_before > 0].to_dict()}")
            # Fill NaN values for all remaining features
            for col in remaining_features:
                primary_df[col] = primary_df[col].fillna(0.0)

    # Extract only the required features in correct order (exactly like live_trading.py)
    log.info(f"🔍 DEBUG: Using EXPECTED_FEATURE_COLS: {len(EXPECTED_FEATURE_COLS)} features")
    primary_df = primary_df[EXPECTED_FEATURE_COLS].astype("float32")
    log.info(f"🔍 DEBUG: primary_df after selection: {primary_df.shape[1]} features")

    # Update feature_cols to use EXPECTED_FEATURE_COLS
    feature_cols = EXPECTED_FEATURE_COLS.copy()

    # Overenie existencie stĺpca pre dynamickú veľkosť pozície
    if "depth_imbalance5" in primary_df.columns:
        config['runtime']['has_imbalance'] = True
    else:
        log.warning("Stĺpec 'depth_imbalance5' chýba v dátach, dynamický size boost nebude použitý.")
        config['runtime']['has_imbalance'] = False


    # All features are now properly processed and selected
    log.info(f"Počet features prítomných v DataFrame: {len(feature_cols)}")

    # Definícia esenciálnych stĺpcov pre dropna
    essential_cols = feature_cols + ['open', 'high', 'low', 'close']

    # Nájdenie a pridanie ATR stĺpca
    atr_col_name = config.get('runtime', {}).get('atr_column')
    if not atr_col_name:
        # Skús nájsť stĺpec začínajúci na ATR_, ak nie je definovaný
        config['runtime']['atr_column'] = next((c for c in primary_df.columns if c.startswith('ATR_')), None)
        atr_col_name = config['runtime']['atr_column']

    risk_perc_sizing = config.get('riskManagement', {}).get('positionSizingMethod') == 'RiskPercentage'
    if atr_col_name:
        if atr_col_name in primary_df.columns:
            essential_cols.append(atr_col_name)
            log.info(f"Identifikovaný ATR stĺpec pre SL/veľkosť: {atr_col_name}")
        else:
             log.warning(f"ATR stĺpec '{atr_col_name}' definovaný v configu alebo runtime nie je v dátach!")
             # Ak je potrebný pre sizing, vyhodíme chybu
             if risk_perc_sizing:
                 raise ValueError(f"Chýba ATR stĺpec '{atr_col_name}' potrebný pre RiskPercentage position sizing.")
    elif risk_perc_sizing:
        raise ValueError("Nebol nájdený žiadny ATR stĺpec (napr. 'ATR_14') v dátach, ktorý je potrebný pre RiskPercentage position sizing.")

    # Pridáme aj imbalance stĺpec k esenciálnym, ak existuje a používa sa
    if config['runtime']['has_imbalance']:
        if "depth_imbalance5" in primary_df.columns:
             essential_cols.append("depth_imbalance5")

    # Odstránime duplikáty a zabezpečíme, že pracujeme len s existujúcimi stĺpcami
    essential_cols = list(set(e for e in essential_cols if e in primary_df.columns))
    log.info(f"Počet esenciálnych stĺpcov pre kontrolu NaN: {len(essential_cols)}")
    log.debug(f"Esenciálne stĺpce: {essential_cols}")


    # --- Odstránenie NaN riadkov z ROZŠÍRENÉHO datasetu ---
    initial_rows_extended = len(primary_df)
    primary_df.dropna(subset=essential_cols, inplace=True)
    rows_dropped_extended = initial_rows_extended - len(primary_df)
    if rows_dropped_extended > 0:
        log.info(f"Odstránených {rows_dropped_extended} riadkov kvôli NaN z rozšíreného datasetu (zahrievacie obdobie).")
    if primary_df.empty:
        raise ValueError("Žiadne dáta nezostali po odstránení NaN z rozšíreného datasetu.")

    # --- Orezanie DataFrame na PÔVODNÝ požadovaný časový rámec ---
    log.info(f"Orezávam dáta na pôvodný požadovaný rozsah: {start_date} -> {end_date}")
    primary_df_original_range = primary_df.loc[start_date:end_date].copy() # Použijeme .copy(), aby sme sa vyhli SettingWithCopyWarning

    if primary_df_original_range.empty:
        raise ValueError("Po orezaní na pôvodný časový rozsah nezostali žiadne dáta. Skontrolujte dostupnosť dát a funkčnosť dropna.")

    final_rows = len(primary_df_original_range)
    log.info(f"Finálny počet riadkov pre backtest po zahriatí a orezaní: {final_rows}")

    # Záverečné logovanie stĺpcov
    cols_to_log = primary_df_original_range.columns[:5].tolist() + ['...'] + primary_df_original_range.columns[-5:].tolist()
    log.info(f"Dostupné stĺpce vo finálnom DataFrame ({len(primary_df_original_range.columns)}): {', '.join(cols_to_log)}")

    # Kontrola, či máme dosť dát aspoň pre prvý krok agenta
    if final_rows < state_lookback:
         log.warning(f"Finálny počet riadkov ({final_rows}) je menší ako potrebný lookback agenta ({state_lookback}). Agent nebude môcť urobiť predikciu.")
         # Môžeme tu buď skončiť s chybou, alebo nechať backtest bežať (ale neurobí žiadny krok)
         # raise ValueError("Nedostatok dát pre prvý krok agenta po zahriatí a orezaní.")

    # --- Príprava dát pre 1s rozhodovanie (BEZ LOOK-AHEAD BIAS) ---
    if use_1s_decisions and second_df is not None:
        log.info("Pripravujem dáta pre 1s rozhodovanie - bez look-ahead bias...")
        
        # Debug: Log the date ranges and data availability
        log.info(f"1s data index range: {second_df.index.min()} -> {second_df.index.max()}")
        log.info(f"Requested range: {start_date} -> {end_date}")
        log.info(f"1s data columns: {list(second_df.columns)}")
        
        # Check if required columns exist in 1s data
        required_cols = ['open', 'high', 'low', 'close']
        missing_cols = [col for col in required_cols if col not in second_df.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns in 1s data: {missing_cols}")
        
        # Orezanie 1s dát na pôvodný rozsah - iba OHLCV pre real-time forward-fill
        second_df_original_range = second_df.loc[start_date:end_date][['open', 'high', 'low', 'close']].copy()
        
        log.info(f"Pripravených {len(second_df_original_range)} 1s OHLCV riadkov pre real-time forward-fill.")
        
        if len(second_df_original_range) == 0:
            log.error(f"No 1s data found in range {start_date} -> {end_date}")
            log.error(f"Available 1s data range: {second_df.index.min()} -> {second_df.index.max()}")
            raise ValueError(f"No 1s data available for the requested backtest period. Check if 1s data exists for {start_date.date()} to {end_date.date()}")
        
        log.info("Forward-fill 5m features sa bude robiť REAL-TIME počas simulácie!")
        
        # Vrátime 1s OHLCV dáta ako second, 5m ako primary pre real-time forward-fill
        return {
            'primary': primary_df_original_range,      # 5m dáta s features
            'second': second_df_original_range,        # 1s OHLCV pre real-time decisions
            'use_1s_decisions': True                   # Flag pre real-time forward-fill
        }

    # Vrátime slovník s oboma dataframe-ami (pôvodné správanie)
    return {
        'primary': primary_df_original_range,
        'second': second_df
    }

# <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
# >>>>>>>>>>>>>>>>>>>>>>>>>>>>> UPRAVENÁ FUNKCIA <<<<<<<<<<<<<<<<<<<<<<<<<<<<<
# <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
def get_state(df_features: pd.DataFrame, current_step_index: int, lookback: int,
              current_pos: int, entry_price: float, current_price: float,
              current_sl_price: float, current_tp_price: float,
              open_idx_sim: int, current_atr: float,
              inactivity_limit: int, tsl_enabled: bool,
              use_1s_decisions: bool = False, features_5m: pd.DataFrame = None,
              main_timestamps: pd.Index = None, current_time: pd.Timestamp = None,
              previous_features: pd.Series = None, smoothing_factor: float = 0.3,
              data_1s: pd.DataFrame = None) -> tuple:
    """
    Pripraví stavový vektor podľa logiky ScalpingEnv._state.
    Vracia sploštený vektor tvaru (observation_space.shape[0],).
    
    Ak use_1s_decisions=True, vytvára real-time forward-fill features pre lookback window.
    """
    if current_step_index < lookback - 1:
        raise IndexError(f"Nedostatok histórie ({current_step_index+1}) pre lookback={lookback}.")

    if use_1s_decisions:
        # Real-time forward-fill mode - vytvoríme features pre lookback window
        if features_5m is None or main_timestamps is None or current_time is None:
            raise ValueError("V 1s mode sú potrebné features_5m, main_timestamps a current_time")

        # Získame lookback window timestamps
        start_idx = current_step_index - lookback + 1
        end_idx = current_step_index + 1
        lookback_timestamps = main_timestamps[start_idx:end_idx]

        if len(lookback_timestamps) != lookback:
            raise ValueError(f"Získaných {len(lookback_timestamps)} timestamps namiesto {lookback}")

        # ●●●●●●●● ENHANCED 1S MODE WITH PRICE UPDATES (exactly like live_trading.py) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
        # In live trading, 1s price changes affect the state even when 5m indicators don't change
        # We need to incorporate 1s price data into the state to make it dynamic

        # Pre každý timestamp v lookback window vytvoríme real-time forward-fill features
        frame_data = []
        current_features_for_smoothing = None

        for ts in lookback_timestamps:
            # Nájdi posledné dostupné 5m features pred/na ts (BEZ LOOK-AHEAD)
            # Použijeme .loc namiesto boolean indexing pre zabezpečenie správnej funkčnosti
            try:
                available_5m = features_5m.loc[features_5m.index <= ts]

                if not available_5m.empty:
                    # Vezmi posledný dostupný 5m bar a jeho features (forward-fill)
                    latest_5m_features = available_5m.iloc[-1].copy()

                    # ●●●●●●●● 1S PRICE INJECTION (critical for dynamic state) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                    # Inject current 1s price data into the 5m features to make state dynamic
                    # This simulates how live trading gets fresh price data every second

                    # ●●●●●●●● 1S INJECTION RE-ENABLED ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                    # Re-enable price injection to make state dynamic every second
                    # This ensures agent sees current price changes within 5m bars

                    if ts == lookback_timestamps[-1] and use_1s_decisions:  # Only for current timestamp in 1s mode
                        try:
                            current_1s_row = data_1s.loc[current_time]
                            # Inject current 1s OHLC into 5m features
                            latest_5m_features["close"] = current_1s_row['close']
                            latest_5m_features["high"] = max(latest_5m_features["high"], current_1s_row['high'])
                            latest_5m_features["low"] = min(latest_5m_features["low"], current_1s_row['low'])
                            # Keep original open from 5m bar
                            log.debug(f"🔄 1S PRICE INJECTION @ {ts}: close={current_1s_row['close']:.6f}")
                        except KeyError:
                            log.debug(f"🔄 USING 5M DATA @ {ts}: No 1s data available")

                    # State changes will come from 1s price injection or new 5m bars

                    # ●●●●●●●● FEATURE SMOOTHING (exactly like live_trading.py) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                    # Apply smoothing to the most recent features to prevent sudden changes
                    if ts == lookback_timestamps[-1] and previous_features is not None:
                        # Only smooth the latest timestamp (current decision point)
                        ohlc_fields = ["open", "high", "low", "close"]  # Don't smooth OHLC, keep price changes immediate
                        smoothed_features = latest_5m_features.copy()

                        # Apply smoothing to non-OHLC features (keep price data immediate)
                        for col in latest_5m_features.index:
                            if col not in ohlc_fields and col in previous_features.index:
                                old_val = previous_features[col]
                                new_val = latest_5m_features[col]

                                # Only smooth if both values are valid
                                if pd.notna(old_val) and pd.notna(new_val):
                                    # Exponential smoothing: new = old * (1-α) + new * α
                                    smoothed_val = old_val * (1 - smoothing_factor) + new_val * smoothing_factor
                                    smoothed_features[col] = smoothed_val

                        frame_data.append(smoothed_features.values)
                        current_features_for_smoothing = smoothed_features
                    else:
                        # For historical timestamps, use features with price variation
                        frame_data.append(latest_5m_features.values)
                        if ts == lookback_timestamps[-1]:
                            current_features_for_smoothing = latest_5m_features
                else:
                    # Ak ešte žiadne 5m features nie sú dostupné, naplň nulami
                    frame_data.append(np.zeros(len(features_5m.columns)))
            except Exception as e:
                # V prípade chyby naplň nulami
                frame_data.append(np.zeros(len(features_5m.columns)))

        # Konverzia na numpy array
        frame_array = np.array(frame_data, dtype=np.float32)
        
        if frame_array.shape[0] != lookback:
            raise ValueError(f"Real-time forward-fill vytvoril {frame_array.shape[0]} riadkov namiesto {lookback}")
            
    else:
        # Pôvodný 5m mode
        start_idx = current_step_index - lookback + 1
        end_idx = current_step_index + 1
        frame_data = df_features.iloc[start_idx:end_idx]
        
        if len(frame_data) != lookback:
            raise ValueError(f"Získaných {len(frame_data)} riadkov namiesto {lookback} pre index {current_step_index}")

        # Ensure we have a contiguous numpy array with the right dtype
        frame_array = np.ascontiguousarray(frame_data.values).astype(np.float32)

    # Orezanie dát z trhu a sploštenie
    frame_flat_clipped = np.clip(frame_array, -5.0, 5.0).flatten()

    # --- Výpočet dodatočných stavových hodnôt (podľa ScalpingEnv) ---
    _MIN_PRICE = 1e-6
    _PNL_NORM_DENOM = 0.01 # Predpokladáme rovnakú hodnotu ako v env

    safe_current_price = max(current_price, _MIN_PRICE)
    safe_entry_price = max(entry_price, _MIN_PRICE)

    # 1. PnL Normalizované
    pnl_norm = 0.0
    if current_pos != 0 and entry_price > 0:
        pnl_norm = np.tanh(((safe_current_price - safe_entry_price) * current_pos) /
                           (safe_entry_price * _PNL_NORM_DENOM))

    # 2. Trvanie obchodu Normalizované
    trade_duration = 0.0
    if current_pos != 0 and open_idx_sim >= 0 and inactivity_limit > 0:
        trade_duration = np.tanh((current_step_index - open_idx_sim) / (inactivity_limit * 0.5))

    # 3. Status profitu (+1 profit, -1 strata, 0 flat/nula)
    profit_status = 0.0
    if current_pos != 0 and entry_price > 0:
        profit_status = np.sign((safe_current_price - safe_entry_price) * current_pos)

    # 4. & 5. Vzdialenosť od TP a SL Normalizovaná
    tp_distance = 0.0
    sl_distance = 0.0
    if current_pos != 0 and np.isfinite(current_tp_price) and np.isfinite(current_sl_price) and current_tp_price > 0 and current_sl_price > 0:
        tp_norm_factor = safe_current_price * 0.01 # Normalizácia 1%
        sl_norm_factor = safe_current_price * 0.01
        if abs(tp_norm_factor) > 1e-9:
            tp_distance = np.tanh(abs(current_tp_price - safe_current_price) / tp_norm_factor) * np.sign(current_tp_price - safe_current_price) * current_pos
        if abs(sl_norm_factor) > 1e-9:
            sl_distance = np.tanh(abs(current_sl_price - safe_current_price) / sl_norm_factor) * np.sign(current_sl_price - safe_current_price) * current_pos

    # 6. PnL / ATR Ratio Normalizované
    pnl_atr_ratio = 0.0
    # Použijeme current_atr priamo, predpokladáme, že je už validované
    if current_pos != 0 and tsl_enabled and current_atr > 1e-9 and entry_price > 0:
        pnl_atr_ratio = np.tanh(((safe_current_price - safe_entry_price) * current_pos) / current_atr)

    # --- TIME FEATURES podľa ENV ---
    if use_1s_decisions:
        # V 1s mode použijeme current_time parameter
        cur_timestamp = current_time
    else:
        # V 5m mode použijeme df_features index
        cur_timestamp = df_features.index[current_step_index]
    
    cur_hour = cur_timestamp.hour
    dow = cur_timestamp.dayofweek        # 0=Mon … 6=Sun

    time_feats = [
        np.sin(cur_hour / 24 * 2 * np.pi),
        np.cos(cur_hour / 24 * 2 * np.pi),
        np.sin(dow / 7 * 2 * np.pi),
        np.cos(dow / 7 * 2 * np.pi),
    ]

    # --- Spojenie všetkých častí (vrátane time_feats) ---
    state_vector = np.concatenate((
        frame_flat_clipped,           # 1440 prvkov (48 features × 30 lookback)
        [
            float(current_pos),       # 1
            pnl_norm,                 # 2
            trade_duration,           # 3
            profit_status,            # 4
            tp_distance,              # 5
            sl_distance,              # 6
            pnl_atr_ratio             # 7
        ],
        time_feats                   # 4 time features
    )).astype(np.float32)             # Celkom 1440 + 7 + 4 = 1451 prvkov

    # Return both state vector and current features for smoothing
    return state_vector, current_features_for_smoothing

def generate_parameter_combinations():
    """
    Generuje všetky kombinácie parametrov pre testovanie.
    Vracia list dictionaries s rôznymi kombinaciami parametrov.
    """
    # Definícia hodnôt pre testovanie
    activate_atr_multiplier_values = [0.5, 1.0, 1.5]  # Reduced for faster testing
    trail_atr_multiplier_values = [0.35, 0.5]  # Reduced for faster testing
    min_atr_percent_values = [0.1, 0.3]  # Reduced for faster testing
    min_sl_distance_atr_values = [2.0, 2.5]  # Reduced for faster testing
    min_sl_distance_percent_values = [1.0, 2.0]  # Reduced for faster testing
    rr_target_values = [1.5, 2.0, 2.5]  # Higher values as per user preference

    # NEW: Entry/Exit threshold optimization
    long_entry_threshold_values = [0.5, 0.6, 0.7, 0.8]
    short_entry_threshold_values = [0.5, 0.6, 0.7, 0.8]
    exit_threshold_values = [0.5, 0.6, 0.7, 0.8]

    combinations = []

    # Generovanie všetkých kombinácií
    for activate_atr in activate_atr_multiplier_values:
        for trail_atr in trail_atr_multiplier_values:
            for min_atr_perc in min_atr_percent_values:
                for min_sl_atr in min_sl_distance_atr_values:
                    for min_sl_perc in min_sl_distance_percent_values:
                        for rr_target in rr_target_values:
                            for long_entry_thr in long_entry_threshold_values:
                                for short_entry_thr in short_entry_threshold_values:
                                    for exit_thr in exit_threshold_values:
                                        combinations.append({
                                            'activateATRMultiplier': activate_atr,
                                            'trailATRMultiplier': trail_atr,
                                            'minAtrPercentOfPrice': min_atr_perc,
                                            'minSLDistanceATR': min_sl_atr,
                                            'minSLDistancePercent': min_sl_perc,
                                            'rrTarget': rr_target,
                                            'longEntryThreshold': long_entry_thr,
                                            'shortEntryThreshold': short_entry_thr,
                                            'exitActionThreshold': exit_thr
                                        })

    log.info(f"Vygenerovaných {len(combinations)} kombinácií parametrov")
    return combinations

def apply_trend_filter(df_features: pd.DataFrame, current_step_index: int,
                      entry_signal: float, config: dict) -> tuple:
    """
    Aplikuje trend filter na základe rolling Supertrend a ADX indikátorov.

    Args:
        df_features: DataFrame s features vrátane rolling indikátorov
        current_step_index: Aktuálny index v DataFrame
        entry_signal: Signál od agenta (pozitívny = long, negatívny = short)
        config: Konfigurácia s trendFilter nastaveniami

    Returns:
        tuple: (allowed, reason) - či je vstup povolený a dôvod
    """
    trend_filter_config = config.get('indicatorSettings', {}).get('trendFilter', {})

    # Ak je trend filter vypnutý, povoliť všetko
    if not trend_filter_config.get('enabled', False):
        return True, "trend_filter_disabled"

    # Kontrola metódy
    method = trend_filter_config.get('method', '')
    if method != 'supertrend_adx':
        return True, f"unsupported_method_{method}"

    # Získanie parametrov
    params = trend_filter_config.get('params', {})
    min_adx = params.get('adx', {}).get('minAdx', 25)

    # Kontrola dostupnosti rolling indikátorov
    if 'supertrend_1h_rolling' not in df_features.columns or 'adx_1h_rolling' not in df_features.columns:
        return True, "rolling_indicators_missing"

    # Získanie aktuálnych hodnôt
    if current_step_index >= len(df_features):
        return True, "index_out_of_range"

    current_price = df_features['close'].iloc[current_step_index]
    supertrend_value = df_features['supertrend_1h_rolling'].iloc[current_step_index]
    adx_value = df_features['adx_1h_rolling'].iloc[current_step_index]

    # Kontrola na NaN hodnoty
    if pd.isna(supertrend_value) or pd.isna(adx_value):
        return True, "indicators_nan"

    # Kontrola sily trendu (ADX)
    if adx_value < min_adx:
        return False, f"weak_trend_adx_{adx_value:.1f}<{min_adx}"

    # Určenie smeru trendu na základe Supertrend
    is_bullish_trend = current_price > supertrend_value
    is_bearish_trend = current_price < supertrend_value

    # Filtrovanie na základe smeru signálu a trendu
    if entry_signal > 0:  # Long signál
        if not is_bullish_trend:
            return False, f"long_against_trend_price_{current_price:.5f}_st_{supertrend_value:.5f}"
        return True, f"long_with_trend_adx_{adx_value:.1f}"

    elif entry_signal < 0:  # Short signál
        if not is_bearish_trend:
            return False, f"short_against_trend_price_{current_price:.5f}_st_{supertrend_value:.5f}"
        return True, f"short_with_trend_adx_{adx_value:.1f}"

    # Neutrálny signál
    return True, "neutral_signal"

def calculate_dynamic_thresholds(current_atr, avg_atr, base_long_thr, base_short_thr, base_exit_thr, volatility_adjustment_factor=0.1):
    """
    Dynamically adjusts entry/exit thresholds based on current market volatility (ATR).

    Args:
        current_atr: Current ATR value
        avg_atr: Average ATR over recent period (e.g., 20 periods)
        base_long_thr: Base long entry threshold from config
        base_short_thr: Base short entry threshold from config
        base_exit_thr: Base exit threshold from config
        volatility_adjustment_factor: How much to adjust thresholds based on volatility

    Returns:
        Tuple of (adjusted_long_thr, adjusted_short_thr, adjusted_exit_thr)
    """
    if avg_atr == 0 or np.isnan(avg_atr) or np.isnan(current_atr):
        return base_long_thr, base_short_thr, base_exit_thr

    # Calculate volatility ratio (current vs average)
    volatility_ratio = current_atr / avg_atr

    # Adjust thresholds: Lower in high volatility (more opportunities), higher in low volatility
    adjustment = (volatility_ratio - 1.0) * volatility_adjustment_factor

    # Apply adjustment (inverse relationship - higher volatility = lower thresholds)
    adjusted_long_thr = np.clip(base_long_thr - adjustment, 0.3, 0.9)
    # For short threshold: base_short_thr is negative (e.g., -0.85), so we add adjustment to make it less negative
    adjusted_short_thr = np.clip(base_short_thr + adjustment, -0.9, -0.3)
    adjusted_exit_thr = np.clip(base_exit_thr - adjustment, 0.3, 0.9)

    return adjusted_long_thr, adjusted_short_thr, adjusted_exit_thr

def generate_threshold_evolution_graph(threshold_evolution, symbol, start_date, end_date):
    """
    Generate a graph showing threshold evolution over time during simulation.

    Args:
        threshold_evolution: Dictionary containing threshold evolution data
        symbol: Trading symbol
        start_date: Start date of simulation
        end_date: End date of simulation
    """
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from datetime import datetime

    if not threshold_evolution['timestamps']:
        print("No threshold evolution data to plot")
        return

    # Convert timestamps to datetime objects if they aren't already
    timestamps = []
    for ts in threshold_evolution['timestamps']:
        if isinstance(ts, str):
            timestamps.append(pd.to_datetime(ts))
        else:
            timestamps.append(ts)

    # Create the figure with subplots
    fig, axes = plt.subplots(5, 1, figsize=(15, 16))
    fig.suptitle(f'Threshold & Signal Evolution - {symbol} ({start_date} to {end_date})', fontsize=16)

    # Plot 1: Entry Signal Evolution (Main focus)
    ax1 = axes[0]
    ax1.plot(timestamps, threshold_evolution['entry_signals'], label='Entry Signal', color='black', linewidth=2, alpha=0.8)
    ax1.axhline(y=0, color='gray', linestyle='-', alpha=0.5, linewidth=1)
    ax1.axhline(y=0.6, color='green', linestyle='--', alpha=0.7, linewidth=1, label='Long Threshold (0.6)')
    ax1.axhline(y=-0.4, color='red', linestyle='--', alpha=0.7, linewidth=1, label='Short Threshold (-0.4)')
    ax1.fill_between(timestamps, 0.6, 1, alpha=0.1, color='green', label='Long Entry Zone')
    ax1.fill_between(timestamps, -1, -0.4, alpha=0.1, color='red', label='Short Entry Zone')
    ax1.set_ylabel('Signal Value')
    ax1.set_title('Entry Signal Evolution (entry_sig)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(-1.2, 1.2)

    # Plot 2: Entry Thresholds
    ax2 = axes[1]
    ax2.plot(timestamps, threshold_evolution['long_thresholds'], label='Long Entry Threshold', color='green', alpha=0.7)
    ax2.plot(timestamps, threshold_evolution['short_thresholds'], label='Short Entry Threshold', color='red', alpha=0.7)
    if threshold_evolution['adaptive_long_thresholds']:
        ax2.plot(timestamps, threshold_evolution['adaptive_long_thresholds'], label='Adaptive Long Threshold', color='darkgreen', linewidth=2)
        ax2.plot(timestamps, threshold_evolution['adaptive_short_thresholds'], label='Adaptive Short Threshold', color='darkred', linewidth=2)
    ax2.set_ylabel('Threshold Value')
    ax2.set_title('Entry Thresholds Evolution')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # Plot 3: Exit Thresholds
    ax3 = axes[2]
    ax3.plot(timestamps, threshold_evolution['exit_thresholds'], label='Exit Threshold', color='blue', alpha=0.7)
    if threshold_evolution['adaptive_exit_thresholds']:
        ax3.plot(timestamps, threshold_evolution['adaptive_exit_thresholds'], label='Adaptive Exit Threshold', color='darkblue', linewidth=2)
    ax3.set_ylabel('Threshold Value')
    ax3.set_title('Exit Thresholds Evolution')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # Plot 4: ATR and Volatility
    ax4 = axes[3]
    ax4.plot(timestamps, threshold_evolution['atr_values'], label='Current ATR', color='orange', alpha=0.7)
    ax4.plot(timestamps, threshold_evolution['avg_atr_values'], label='Average ATR', color='darkorange', linewidth=2)
    ax4_twin = ax4.twinx()
    ax4_twin.plot(timestamps, threshold_evolution['volatility_ratios'], label='Volatility Ratio', color='purple', alpha=0.7)
    ax4.set_ylabel('ATR Value', color='orange')
    ax4_twin.set_ylabel('Volatility Ratio', color='purple')
    ax4.set_title('ATR and Volatility Evolution')
    ax4.legend(loc='upper left')
    ax4_twin.legend(loc='upper right')
    ax4.grid(True, alpha=0.3)

    # Plot 5: Signal Strength Analysis
    ax5 = axes[4]
    # Show signal strength with color coding for threshold breaches
    signal_colors = []
    for sig in threshold_evolution['entry_signals']:
        if sig >= 0.6:
            signal_colors.append('green')  # Long signal
        elif sig <= -0.4:
            signal_colors.append('red')    # Short signal
        else:
            signal_colors.append('gray')   # No signal

    ax5.scatter(timestamps, threshold_evolution['entry_signals'], c=signal_colors, alpha=0.6, s=10)
    ax5.axhline(y=0.6, color='green', linestyle='--', alpha=0.7, linewidth=1)
    ax5.axhline(y=-0.4, color='red', linestyle='--', alpha=0.7, linewidth=1)
    ax5.axhline(y=0, color='gray', linestyle='-', alpha=0.5, linewidth=1)
    ax5.set_ylabel('Signal Value')
    ax5.set_xlabel('Time')
    ax5.set_title('Signal Strength Analysis (Green=Long Entry, Red=Short Entry, Gray=No Entry)')
    ax5.grid(True, alpha=0.3)
    ax5.set_ylim(-1.2, 1.2)

    # Format x-axis for all subplots
    for ax in axes:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=6))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

    plt.tight_layout()

    # Save the graph
    filename = f"threshold_evolution_{symbol}_{start_date}_{end_date}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 Threshold evolution graph saved as: {filename}")

    # Also save the data as CSV for further analysis
    df_evolution = pd.DataFrame({
        'timestamp': timestamps,
        'long_threshold': threshold_evolution['long_thresholds'],
        'short_threshold': threshold_evolution['short_thresholds'],
        'exit_threshold': threshold_evolution['exit_thresholds'],
        'adaptive_long_threshold': threshold_evolution['adaptive_long_thresholds'],
        'adaptive_short_threshold': threshold_evolution['adaptive_short_thresholds'],
        'adaptive_exit_threshold': threshold_evolution['adaptive_exit_thresholds'],
        'atr_value': threshold_evolution['atr_values'],
        'avg_atr_value': threshold_evolution['avg_atr_values'],
        'volatility_ratio': threshold_evolution['volatility_ratios'],
        'entry_signal': threshold_evolution['entry_signals'],
        'exit_signal': threshold_evolution['exit_signals']
    })

    csv_filename = f"threshold_evolution_{symbol}_{start_date}_{end_date}.csv"
    df_evolution.to_csv(csv_filename, index=False)
    print(f"📊 Threshold evolution data saved as: {csv_filename}")

    plt.show()

def simulate_single_trade_with_params(trade_setup, param_combination, config_base, data_1s):
    """
    Simuluje jeden obchod s konkrétnou kombináciou parametrov.
    
    Args:
        trade_setup: Dictionary s informáciami o obchode (entry_time, direction, etc.)
        param_combination: Dictionary s parametrami na testovanie
        config_base: Základná konfigurácia
        data_1s: 1s OHLCV dáta pre simuláciu
        
    Returns:
        Dictionary s výsledkami simulácie
    """
    # Vytvorenie kópie konfigurácie s novými parametrami
    config = deepcopy(config_base)
    
    # Aplikovanie nových parametrov
    config['trailingStopLoss']['activateATRMultiplier'] = param_combination['activateATRMultiplier']
    config['trailingStopLoss']['trailATRMultiplier'] = param_combination['trailATRMultiplier']
    config['tradeParams']['minAtrPercentOfPrice'] = param_combination['minAtrPercentOfPrice']
    config['tradeParams']['minSLDistanceATR'] = param_combination['minSLDistanceATR']
    config['tradeParams']['minSLDistancePercent'] = param_combination['minSLDistancePercent']
    config['tradeParams']['rrTarget'] = param_combination['rrTarget']

    # NEW: Apply threshold parameters if present
    if 'longEntryThreshold' in param_combination:
        config['tradeParams']['longEntryThreshold'] = param_combination['longEntryThreshold']
    if 'shortEntryThreshold' in param_combination:
        config['tradeParams']['shortEntryThreshold'] = param_combination['shortEntryThreshold']
    if 'exitActionThreshold' in param_combination:
        config['tradeParams']['exitActionThreshold'] = param_combination['exitActionThreshold']
    
    # Načítanie parametrov pre simuláciu
    entry_time = trade_setup['entry_time']
    direction = trade_setup['direction']
    entry_price = trade_setup['entry_price']
    current_atr = trade_setup.get('current_atr', 0.001)  # Default fallback ATR
    position = 1 if direction == 'Long' else -1
    
    # Výpočet SL a TP s novými parametrami
    trade_params = config.get('tradeParams', {})
    fee_perc = trade_params.get('feePercentage', 0.0) / 100.0
    slippage_perc = trade_params.get('slippagePercentage', 0.0) / 100.0
    min_sl_dist_perc = param_combination['minSLDistancePercent'] / 100.0
    min_sl_dist_atr_mult = param_combination['minSLDistanceATR']
    rr_target = param_combination['rrTarget']

    # TP mode configuration
    tp_mode = trade_params.get('tpMode', 'rrTarget')  # 'rrTarget', 'capitalPercentage', or 'atr'
    tp_capital_percentage = trade_params.get('tpCapitalPercentage', 1.0) / 100.0  # Convert to decimal

    # ATR-based SL/TP parameters
    stop_loss_atr = trade_params.get('stopLossATR', 0.0)
    take_profit_atr = trade_params.get('takeProfitATR', 0.0)

    # Výpočet SL distance
    if tp_mode == 'atr' and stop_loss_atr > 0 and current_atr > 0:
        # Pure ATR-based SL
        min_sl_dist_points = current_atr * stop_loss_atr
    else:
        # Traditional method: max of percentage and ATR-based
        sl_dist_perc_points = entry_price * min_sl_dist_perc
        sl_dist_atr_points = current_atr * min_sl_dist_atr_mult if current_atr > 0 else 0
        min_sl_dist_points = max(sl_dist_perc_points, sl_dist_atr_points)
    
    if min_sl_dist_points <= 1e-9:
        # Ak je SL distance príliš malá, použijeme minimal fallback
        min_sl_dist_points = entry_price * 0.005  # 0.5% fallback
    
    # Výpočet SL a TP cien
    sl_price = entry_price - min_sl_dist_points if position == 1 else entry_price + min_sl_dist_points

    # Calculate TP based on selected mode
    if tp_mode == 'atr' and take_profit_atr > 0 and current_atr > 0:
        # Pure ATR-based TP
        tp_dist_points = current_atr * take_profit_atr
        tp_price = entry_price + tp_dist_points if position == 1 else entry_price - tp_dist_points
    elif tp_mode == 'capitalPercentage':
        # TP based on percentage of initial capital
        initial_capital = config.get('account', {}).get('initialEquity', 10000)
        target_profit = initial_capital * tp_capital_percentage
        position_size = trade_setup.get('size', 1.0)

        if position_size > 0:
            tp_dist_points = target_profit / position_size
            tp_price = entry_price + tp_dist_points if position == 1 else entry_price - tp_dist_points
        else:
            # Fallback to rrTarget if position size is invalid
            tp_dist_points = min_sl_dist_points * rr_target
            tp_price = entry_price + tp_dist_points if position == 1 else entry_price - tp_dist_points
    else:
        # Default rrTarget mode
        tp_dist_points = min_sl_dist_points * rr_target
        tp_price = entry_price + tp_dist_points if position == 1 else entry_price - tp_dist_points
    
    # TSL parametry - percent-based
    tsl_config = config.get('trailingStopLoss', {})
    tsl_enabled = tsl_config.get('enabled', False)
    tsl_activate_percent = tsl_config.get('activatePercentProfit', 0.8)  # % profit to activate TSL
    tsl_trail_percent = tsl_config.get('trailPercentDistance', 0.3)      # % distance for trailing
    
    # Debug tracking for entry signals
    entry_signals_before_trade = []
    entry_signals_after_trade = []

    # Simulácia obchodu s 1s dátami
    entry_fee = abs(trade_setup.get('size', 1.0) * entry_price * fee_perc)
    current_sl_price = sl_price
    current_tp_price = tp_price
    trailing_active = False
    peak_price_in_trade = entry_price
    max_profit = 0.0
    max_drawdown = 0.0
    
    # Nájdenie entry time v 1s dátach a simulácia od tohto bodu
    try:
        if entry_time not in data_1s.index:
            # Nájdi najbližší dostupný timestamp
            available_times = data_1s.index[data_1s.index >= entry_time]
            if len(available_times) == 0:
                # Ak nie sú dáta dostupné, vrátime neutral result
                return {
                    'trade_id': trade_setup.get('trade_id', 0),
                    'entry_time': entry_time,
                    'direction': direction,
                    'entry_price': entry_price,
                    'exit_time': entry_time,
                    'exit_price': entry_price,
                    'pnl': -entry_fee,  # Iba fees
                    'exit_reason': 'NO_DATA',
                    'param_combination': param_combination,
                    'sl_price': sl_price,
                    'tp_price': tp_price,
                    'max_drawdown': 0.0,
                    'max_profit': 0.0,
                    'time_in_trade_seconds': 0
                }
            entry_time = available_times[0]
        
        # Získanie 1s dát od entry_time ďalej (max 2 hodiny alebo do konca dát)
        end_time = min(entry_time + pd.Timedelta(hours=2), data_1s.index.max())
        trade_data = data_1s.loc[entry_time:end_time]
        
        if len(trade_data) == 0:
            # Ak nie sú dáta dostupné, vrátime neutral result
            return {
                'trade_id': trade_setup.get('trade_id', 0),
                'entry_time': entry_time,
                'direction': direction,
                'entry_price': entry_price,
                'exit_time': entry_time,
                'exit_price': entry_price,
                'pnl': -entry_fee,
                'exit_reason': 'NO_DATA',
                'param_combination': param_combination,
                'sl_price': sl_price,
                'tp_price': tp_price,
                'max_drawdown': 0.0,
                'max_profit': 0.0,
                'time_in_trade_seconds': 0
            }
        
        # Simulácia tick-by-tick
        for current_time, row in trade_data.iterrows():
            current_high = row['high']
            current_low = row['low']
            current_close = row['close']
            
            # Aktuálny P&L a tracking max profit/drawdown
            current_pnl = (current_close - entry_price) * position
            if current_pnl > max_profit:
                max_profit = current_pnl
            if current_pnl < max_drawdown:
                max_drawdown = current_pnl
            
            # Kontrola SL/TP hit
            exit_reason = None
            exit_price = None
            
            if position == 1:  # Long
                hit_sl = current_sl_price > 0 and current_low <= current_sl_price
                hit_tp = current_tp_price > 0 and current_high >= current_tp_price
                
                # SL má prioritu nad TP
                if hit_sl and hit_tp:
                    hit_tp = False
                
                if hit_sl:
                    exit_reason = "SL"
                    exit_price = current_sl_price
                elif hit_tp:
                    exit_reason = "TP"
                    exit_price = current_tp_price
                
                # TSL update pre Long - percent-based
                if not exit_reason and tsl_enabled:
                    profit = current_high - entry_price
                    profit_percent = (profit / entry_price) * 100 if entry_price > 0 else 0

                    if not trailing_active and profit_percent >= tsl_activate_percent:
                        trailing_active = True
                        trail_distance = entry_price * (tsl_trail_percent / 100)
                        current_sl_price = current_high - trail_distance
                        peak_price_in_trade = current_high
                    elif trailing_active:
                        trail_distance = entry_price * (tsl_trail_percent / 100)
                        new_sl = current_high - trail_distance
                        if new_sl > current_sl_price:
                            current_sl_price = new_sl
                
            else:  # Short
                hit_sl = current_sl_price > 0 and current_high >= current_sl_price
                hit_tp = current_tp_price > 0 and current_low <= current_tp_price
                
                # SL má prioritu nad TP
                if hit_sl and hit_tp:
                    hit_tp = False
                
                if hit_sl:
                    exit_reason = "SL"
                    exit_price = current_sl_price
                elif hit_tp:
                    exit_reason = "TP"
                    exit_price = current_tp_price
                
                # TSL update pre Short - percent-based
                if not exit_reason and tsl_enabled:
                    profit = entry_price - current_low
                    profit_percent = (profit / entry_price) * 100 if entry_price > 0 else 0

                    if not trailing_active and profit_percent >= tsl_activate_percent:
                        trailing_active = True
                        trail_distance = entry_price * (tsl_trail_percent / 100)
                        current_sl_price = current_low + trail_distance
                        peak_price_in_trade = current_low
                    elif trailing_active:
                        trail_distance = entry_price * (tsl_trail_percent / 100)
                        new_sl = current_low + trail_distance
                        if new_sl < current_sl_price:
                            current_sl_price = new_sl
            
            # Ak bol hit SL/TP, ukončujeme obchod
            if exit_reason:
                sim_exit_price = exit_price * (1 - slippage_perc * position)
                pnl = (sim_exit_price - entry_price) * position
                exit_fee = abs(trade_setup.get('size', 1.0) * sim_exit_price * fee_perc)
                net_pnl = pnl - entry_fee - exit_fee
                time_in_trade = (current_time - entry_time).total_seconds()
                
                return {
                    'trade_id': trade_setup.get('trade_id', 0),
                    'entry_time': entry_time,
                    'direction': direction,
                    'entry_price': entry_price,
                    'exit_time': current_time,
                    'exit_price': sim_exit_price,
                    'pnl': net_pnl,
                    'exit_reason': exit_reason,
                    'param_combination': param_combination,
                    'sl_price': sl_price,
                    'tp_price': tp_price,
                    'max_drawdown': max_drawdown,
                    'max_profit': max_profit,
                    'time_in_trade_seconds': time_in_trade
                }
        
        # Ak sme nedosiahli SL/TP, ukončujeme na poslednej cene (timeout)
        final_row = trade_data.iloc[-1]
        final_price = final_row['close'] * (1 - slippage_perc * position)
        pnl = (final_price - entry_price) * position
        exit_fee = abs(trade_setup.get('size', 1.0) * final_price * fee_perc)
        net_pnl = pnl - entry_fee - exit_fee
        time_in_trade = (trade_data.index[-1] - entry_time).total_seconds()
        
        return {
            'trade_id': trade_setup.get('trade_id', 0),
            'entry_time': entry_time,
            'direction': direction,
            'entry_price': entry_price,
            'exit_time': trade_data.index[-1],
            'exit_price': final_price,
            'pnl': net_pnl,
            'exit_reason': 'TIMEOUT',
            'param_combination': param_combination,
            'sl_price': sl_price,
            'tp_price': tp_price,
            'max_drawdown': max_drawdown,
            'max_profit': max_profit,
            'time_in_trade_seconds': time_in_trade
        }
        
    except Exception as e:
        # V prípade chyby vrátime neutrálny výsledok
        return {
            'trade_id': trade_setup.get('trade_id', 0),
            'entry_time': entry_time,
            'direction': direction,
            'entry_price': entry_price,
            'exit_time': entry_time,
            'exit_price': entry_price,
            'pnl': -entry_fee,
            'exit_reason': f'ERROR: {str(e)}',
            'param_combination': param_combination,
            'sl_price': sl_price,
            'tp_price': tp_price,
            'max_drawdown': 0.0,
            'max_profit': 0.0,
            'time_in_trade_seconds': 0
        }

def run_backtest_with_parameter_sweep(config: dict, data_dict: dict, agent, vecnorm=None, use_1s_decisions: bool = False):
    """
    Spustí backtest s parameter sweep - pre každý obchod testuje rôzne kombinácie parametrov.
    """
    log.info("🧪 Začínam backtest s parameter sweep...")
    
    # Generovanie kombinácií parametrov
    parameter_combinations = generate_parameter_combinations()
    
    # Príprava súboru pre výsledky
    results_file = f"parameter_sweep_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    # Hlavička CSV súboru
    csv_columns = [
        'trade_id', 'entry_time', 'direction', 'entry_price', 'exit_time', 'exit_price',
        'pnl', 'exit_reason', 'sl_price', 'tp_price', 'max_drawdown', 'max_profit',
        'time_in_trade_seconds', 'activateATRMultiplier', 'trailATRMultiplier',
        'minAtrPercentOfPrice', 'minSLDistanceATR', 'minSLDistancePercent', 'rrTarget'
    ]
    
    # Spustenie pôvodného backtestu na získanie trade setups
    original_trades, original_equity_curve, original_final_equity, _ = run_backtest_original(
        config, backtest_data, agent, vecnorm, use_1s_decisions
    )
    
    log.info(f"🎯 Našiel som {len(original_trades)} obchodov na testovanie parametrov")
    
    # Výsledky pre všetky kombinácie
    all_results = []
    
    # Pre každý obchod testujeme všetky kombinácie parametrov
    for trade_idx, trade in enumerate(original_trades):
        log.info(f"📊 Testujem obchod {trade_idx + 1}/{len(original_trades)}: {trade['direction']} @ {trade['entry_time']}")
        
        # Príprava trade setup
        trade_setup = {
            'trade_id': trade_idx,
            'entry_time': trade['entry_time'],
            'direction': trade['direction'],
            'entry_price': trade['entry_price'],
            'size': trade['size']
        }
        
        # Testovanie všetkých kombinácií parametrov pre tento obchod
        for combo_idx, param_combo in enumerate(parameter_combinations):
            if combo_idx % 100 == 0:  # Progress update
                log.info(f"  Kombinácia {combo_idx + 1}/{len(parameter_combinations)}")
            
            # Simulácia obchodu s týmito parametrami
            result = simulate_single_trade_with_params(trade_setup, param_combo, config, data_dict['second'])
            all_results.append(result)
    
    # Uloženie výsledkov
    results_df = pd.DataFrame(all_results)
    
    # Flatten parameter combination do separate columns
    for param_name in parameter_combinations[0].keys():
        results_df[param_name] = results_df['param_combination'].apply(lambda x: x[param_name])
    
    # Remove the nested param_combination column
    results_df = results_df.drop(columns=['param_combination'])
    
    # Uloženie do CSV
    results_df.to_csv(results_file, index=False, float_format='%.6f')
    log.info(f"💾 Výsledky parameter sweep uložené do: {results_file}")
    
    # Analýza najlepších kombinácií
    analyze_parameter_sweep_results(results_df)
    
    return original_trades, original_equity_curve, original_final_equity, results_df

def analyze_parameter_sweep_results(results_df):
    """Analyzuje výsledky parameter sweep a nájde najlepšie kombinácie."""
    log.info("📈 Analyzujem výsledky parameter sweep...")
    
    # Zoskupenie podľa kombinácií parametrov
    param_columns = ['activateATRMultiplier', 'trailATRMultiplier', 'minAtrPercentOfPrice', 
                    'minSLDistanceATR', 'minSLDistancePercent', 'rrTarget']
    
    grouped = results_df.groupby(param_columns).agg({
        'pnl': ['sum', 'mean', 'count', 'std'],
        'exit_reason': lambda x: (x == 'TP').sum() / len(x) * 100,  # Win rate
        'time_in_trade_seconds': 'mean'
    }).round(4)
    
    grouped.columns = ['total_pnl', 'avg_pnl', 'trade_count', 'pnl_std', 'win_rate_pct', 'avg_time_in_trade']
    grouped = grouped.reset_index()
    
    # Najlepšie kombinácie podľa celkového PnL
    top_combinations = grouped.nlargest(10, 'total_pnl')
    
    log.info("🏆 TOP 10 kombinácií parametrov (podľa celkového PnL):")
    for idx, row in top_combinations.iterrows():
        log.info(f"  {idx+1}. Total PnL: {row['total_pnl']:.2f} | "
                f"Avg PnL: {row['avg_pnl']:.2f} | "
                f"Win Rate: {row['win_rate_pct']:.1f}% | "
                f"Trades: {row['trade_count']}")
        log.info(f"     Params: activate={row['activateATRMultiplier']:.1f}, "
                f"trail={row['trailATRMultiplier']:.2f}, "
                f"minAtr%={row['minAtrPercentOfPrice']:.1f}, "
                f"SL_ATR={row['minSLDistanceATR']:.1f}, "
                f"SL%={row['minSLDistancePercent']:.1f}, "
                f"RR={row['rrTarget']:.1f}")
        # Show threshold parameters if they exist
        if 'longEntryThreshold' in row:
            log.info(f"     Thresholds: Long={row['longEntryThreshold']:.2f}, "
                    f"Short={row['shortEntryThreshold']:.2f}, "
                    f"Exit={row['exitActionThreshold']:.2f}")
    
    # Uloženie analýzy
    analysis_file = f"parameter_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    grouped.to_csv(analysis_file, index=False, float_format='%.4f')
    log.info(f"💾 Analýza parametrov uložená do: {analysis_file}")
    
    return top_combinations

def run_backtest_original(config: dict, data_dict: dict, agent, vecnorm=None, use_1s_decisions: bool = False):
    """Kompletná funkcia run_backtest - identická s simulate_trading.py."""
    # --- Načítanie parametrov (ako v ScalpingEnv) ---
    initial_equity = config.get('account', {}).get('initialEquity', 10000)
    try:
        feature_cols = config['envSettings']['feature_columns']
        lookback = config['envSettings']['state_lookback']
        inactivity_limit = config['envSettings'].get('inactivity_limit', 600)
        max_ep_len = config['envSettings'].get('max_ep_len', 3_000)
    except KeyError as e: raise ValueError(f"Chýba kľúč v envSettings: {e}")

    trade_params = config.get('tradeParams', {})
    fee_perc = trade_params.get('feePercentage', 0.0) / 100.0
    slippage_perc = trade_params.get('slippagePercentage', 0.0) / 100.0

    # TP mode configuration
    tp_mode = trade_params.get('tpMode', 'rrTarget')  # 'rrTarget' or 'capitalPercentage'
    tp_capital_percentage = trade_params.get('tpCapitalPercentage', 1.0) / 100.0  # Convert to decimal
    # Support both old and new SL configuration methods
    if 'stopLossCapitalPercent' in trade_params:
        # New simple method: SL as percentage of capital
        stop_loss_capital_percent = trade_params.get('stopLossCapitalPercent', 1.0) / 100.0
        min_sl_dist_perc = 0.0  # Will be calculated dynamically
        min_sl_dist_atr_mult = 0.0  # Not used in new method
    else:
        # Old method: ATR and percentage distances
        min_sl_dist_perc = trade_params.get('minSLDistancePercent', 0.0) / 100.0
        min_sl_dist_atr_mult = trade_params.get('minSLDistanceATR', 1.0)
        stop_loss_capital_percent = None
    min_atr_perc_price_threshold = trade_params.get('minAtrPercentOfPrice', 0.0) / 100.0
    rr_target = trade_params.get('rrTarget', 2.0)  # <<<< KĽÚČOVÝ PARAMETER
    
    # Asymetrické entry thresholds - ako v env
    entry_thr = trade_params.get('entryActionThreshold', 0.5)
    long_entry_thr = trade_params.get('longEntryThreshold', entry_thr)
    short_entry_thr = trade_params.get('shortEntryThreshold', entry_thr)
    exit_thr = trade_params.get('exitActionThreshold', 0.4)
    
    # NOVÉ: Agent-based exit control
    agent_exits_enabled = trade_params.get('agentExitsEnabled', True)
    min_time_in_trade = trade_params.get('minTimeInTradeSeconds', 30)
    
    # Store base thresholds for dynamic adjustment
    base_long_entry_thr = long_entry_thr
    base_short_entry_thr = short_entry_thr
    base_exit_thr = exit_thr

    # Dynamic threshold settings
    enable_dynamic_thresholds = trade_params.get('enableDynamicThresholds', False)
    volatility_adjustment_factor = trade_params.get('volatilityAdjustmentFactor', 0.1)

    # ●●●●●●●● THRESHOLD LOGGING (exactly like live_trading.py TradeExecutor) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
    log.info(f"🎯 TRADING THRESHOLDS INITIALIZED:")
    log.info(f"   Entry thresholds - Long: {long_entry_thr}, Short: {short_entry_thr}, Exit: {exit_thr}")
    log.info(f"   Dynamic thresholds enabled: {enable_dynamic_thresholds}")
    log.info(f"   Agent exits enabled: {agent_exits_enabled}, Min time in trade: {min_time_in_trade}s")
    log.info(f"   Risk-Reward Target: {rr_target}")
    if not agent_exits_enabled:
        log.info("🎯 PRICE-BASED EXITS ONLY: Positions will exit via SL/TP/TSL, agent exit signals IGNORED")
    else:
        log.info("🤖 AGENT-CONTROLLED EXITS: Agent exit signals will be processed alongside SL/TP/TSL")

    risk_mgmt = config.get('riskManagement', {})
    position_sizing_method = risk_mgmt.get('positionSizingMethod', 'RiskPercentage')
    risk_per_trade = risk_mgmt.get('riskPerTradePercentage', 0.0) / 100.0
    max_pos_perc_equity = risk_mgmt.get('maxPositionSizePercentEquity', 100.0) / 100.0

    tsl_config = config.get('trailingStopLoss', {})
    tsl_enabled = tsl_config.get('enabled', False)
    tsl_activate_percent = tsl_config.get('activatePercentProfit', 0.8)  # % profit to activate TSL
    tsl_trail_percent = tsl_config.get('trailPercentDistance', 0.3)      # % distance for trailing

    atr_col = config.get('runtime', {}).get('atr_column')
    has_imbalance_col = config.get('runtime', {}).get('has_imbalance', False)
    
    # >>> NOVÉ: Time-of-day blackout hours (ako v ScalpingEnv)
    DISALLOWED_HOURS = set(config.get('blackoutHours', [0, 1, 2, 3]))  # Default: 00:00-03:59 UTC

    # >>> NOVÉ: Date-based blackout (e.g., first day of month for XRP escrow)
    BLACKOUT_DATES = config.get('blackoutDates', [])  # e.g., ["monthday==1"]
    
    # >>> NOVÉ: Reward structure pre sophisticated hold logic
    reward_structure = config.get('trainingSettings', {}).get('rewardStructure', {})
    
    # >>> NOVÉ: Check spread and volatility filters (ako v ScalpingEnv)
    spread_filter_enabled = config.get('spreadFilter', {}).get('enabled', False)
    volatility_filter_enabled = config.get('volatilityFilter', {}).get('enabled', False)

    # --- Načítanie agenta a jeho observation space ---
    EXPECTED_AGENT_INPUT_SIZE = None; agent_obs_low = -5.0; agent_obs_high = 5.0
    if hasattr(agent, 'observation_space'):
         if isinstance(agent.observation_space, (gym_spaces.spaces.Box if gym_spaces else None.__class__)):
              obs_shape = agent.observation_space.shape
              if len(obs_shape) == 1:
                  EXPECTED_AGENT_INPUT_SIZE = obs_shape[0]
                  # --- OPRAVA: Správne získanie low/high pre skalárne alebo vektorové hranice ---
                  if isinstance(agent.observation_space.low, np.ndarray):
                      agent_obs_low = agent.observation_space.low.min() # Najnižšia možná hodnota across all dimensions
                  else:
                      agent_obs_low = agent.observation_space.low
                  if isinstance(agent.observation_space.high, np.ndarray):
                      agent_obs_high = agent.observation_space.high.max() # Najvyššia možná hodnota across all dimensions
                  else:
                      agent_obs_high = agent.observation_space.high

                  log.info(f"Agent obs space: Box({agent_obs_low}, {agent_obs_high}, ({EXPECTED_AGENT_INPUT_SIZE},), float32)")
              else:
                  log.warning(f"Agent obs space má tvar {obs_shape}."); EXPECTED_AGENT_INPUT_SIZE = np.prod(obs_shape); log.warning(f"Predpokladaná veľkosť vstupu {EXPECTED_AGENT_INPUT_SIZE}.")
         else: log.warning(f"Agent obs space nie je Box ({type(agent.observation_space)}).")
    else: log.warning("Nepodarilo sa získať observation_space."); EXPECTED_AGENT_INPUT_SIZE = 1597; log.warning(f"Používam manuálnu veľkosť: {EXPECTED_AGENT_INPUT_SIZE} a hranice [{agent_obs_low}, {agent_obs_high}]")
    if EXPECTED_AGENT_INPUT_SIZE is None: raise ValueError("Nepodarilo sa určiť očakávanú veľkosť vstupu agenta.")

    # --- Stav simulácie (rozšírený ako v ScalpingEnv) ---
    equity_total = initial_equity  # Celková equity across episodes
    max_equity = initial_equity    # Max equity tracker
    equity_ep = 0.0               # Episode-specific equity
    position = 0; position_size = 0.0; entry_price = 0.0; entry_time = None; entry_fee = 0.0;
    current_sl_price = 0.0; original_sl_price = 0.0; current_tp_price = 0.0; current_tsl_price = 0.0; peak_price_in_trade = 0.0;
    open_idx_sim = -1
    cooldown_counter = 0
    trailing_active = False  # TSL activation flag
    entry_atr = 0.0         # ATR at entry
    atr_history = []        # For ATR ratio calculation in adaptive smoothing
    
    # >>> NOVÉ: Daily risk management (ako v env)
    daily_pnl = 0.0
    daily_risk_used = 0.0
    daily_trade_count = 0
    last_day = None
    trading_allowed = True
    
    trades = []; equity_curve = []

    # --- Získanie dát z data_dict ---
    data_5m = data_dict['primary']
    data_1s = data_dict['second']
    has_1s_data = data_1s is not None
    use_1s_decisions = data_dict.get('use_1s_decisions', False)

    # Debug tracking for entry signals around trades
    entry_signals_log = []
    trade_events = []

    log.info("Začínam backtest..."); log.info(f"Počiatočný kapitál: ${equity_total:.2f}"); log.info(f"Obdobie: {data_5m.index.min()} -> {data_5m.index.max()}");
    log.info(f"Počet krokov 5m: {len(data_5m)}"); log.info(f"Počet krokov 1s: {len(data_1s) if has_1s_data else 0}");
    log.info(f"Lookback: {lookback}"); log.info(f"Agent očakáva vstup: {EXPECTED_AGENT_INPUT_SIZE} v [{agent_obs_low}, {agent_obs_high}]")

    # --- Príprava dát ---
    # All features are now properly processed and available
    log.info(f"Používam {len(feature_cols)} features.")

    # --- PRÍPRAVA PRE REAL-TIME FORWARD-FILL ---
    if use_1s_decisions:
        log.info("🚀 REAL-TIME FORWARD-FILL MODE: Agent robí rozhodnutia každú sekundu")
        log.info("❌ LOOK-AHEAD BIAS ELIMINATED: Forward-fill sa robí v každom kroku simulácie")

        # Pripravíme zdrojové 5m features a 1s timestamps
        features_5m = data_5m[feature_cols].copy()
        
        # Debug: Check data_1s before creating main_timestamps
        log.info(f"DEBUG: data_1s type: {type(data_1s)}")
        log.info(f"DEBUG: data_1s shape: {data_1s.shape}")
        log.info(f"DEBUG: data_1s index length: {len(data_1s.index)}")
        if len(data_1s) > 0:
            log.info(f"DEBUG: data_1s index range: {data_1s.index.min()} -> {data_1s.index.max()}")
            log.info(f"DEBUG: data_1s columns: {list(data_1s.columns)}")
        else:
            log.error("DEBUG: data_1s is empty!")
        
        main_timestamps = data_1s.index
        
        # V 1s mode NEBUDE predpripravený df_features - robí sa real-time!
        df_features = None  # Bude sa vytvárať dynamicky v každom kroku
        
        log.info(f"✅ Pripravené pre real-time forward-fill: {len(main_timestamps)} 1s timestamps, {len(features_5m)} 5m barov")
        
    else:
        # Pôvodný 5m mode
        log.info("📊 STANDARD 5M MODE: Agent robí rozhodnutia každých 5 minút")
        df_features = data_5m[feature_cols].copy()
        main_timestamps = data_5m.index
        features_5m = None  # Nepoužíva sa v 5m mode

    # --- Príprava pre hlavnú slučku ---
    if use_1s_decisions:
        num_features_from_data = len(feature_cols)
        main_length = len(main_timestamps)
        log.info(f"1s mode: {num_features_from_data} features, {main_length} timestamps")

        # Debug: Check why main_timestamps might be empty
        if main_length == 0:
            log.error("main_timestamps is empty in 1s mode!")
            log.error(f"main_timestamps type: {type(main_timestamps)}")
            log.error(f"second_df length: {len(data_1s) if data_1s is not None else 'None'}")
            if data_1s is not None:
                log.error(f"second_df index range: {data_1s.index.min()} -> {data_1s.index.max()}")
                log.error(f"second_df shape: {data_1s.shape}")
            log.error(f"feature_cols: {feature_cols}")
    else:
        num_features_from_data = df_features.shape[1]
        main_length = len(df_features)
        log.info(f"5m mode: {num_features_from_data} features, {main_length} timestamps")
    
    expected_flat_size_from_data = lookback * num_features_from_data
    log.info(f"Počet features z dát: {num_features_from_data}"); log.info(f"Veľkosť splošteného stavu z dát: {expected_flat_size_from_data}")

    # ●●●●●●●● FULLY DETERMINISTIC SEED MANAGEMENT FOR REPRODUCIBLE SIMULATIONS ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
    # For simulate_trading: MUST be deterministic - same data = same results
    import random
    import hashlib

    # CRITICAL FIX: Use only start date for seed to ensure consistent trades across different backtest lengths
    # This ensures that trades from shorter periods (e.g., 1-2 July) will also execute in longer periods (e.g., 1-6 July)
    date_string = f"{start_dt.date()}"  # Use only start date, not end date
    deterministic_hash = int(hashlib.md5(date_string.encode()).hexdigest()[:8], 16)
    base_simulation_seed = deterministic_hash % (2**31)  # Use deterministic hash
    log.info(f"🎲 BASE SIMULATION SEED: {base_simulation_seed} (deterministic hash of: {date_string})")
    log.info(f"🔒 CONSISTENT TRADES: Same seed for all backtests starting on {start_dt.date()}")

    # Set global seeds using the deterministic base seed (not fixed 42!)
    # This ensures different seeds for different start dates while maintaining reproducibility
    random.seed(base_simulation_seed)
    np.random.seed(base_simulation_seed)
    torch.manual_seed(base_simulation_seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(base_simulation_seed)
        torch.cuda.manual_seed_all(base_simulation_seed)

    # Force deterministic behavior globally
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    log.info(f"🔒 GLOBAL DETERMINISTIC SEED SET: {base_simulation_seed} (based on start date)")
    log.info(f"🔒 BASE SEED DETERMINISTIC: {base_simulation_seed} (will be same across runs for same start date)")

    # ●●●●●●●● INTERVAL CONTROL SETUP (exactly like live_trading.py) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
    # Adjust evaluation intervals based on decision mode
    if use_1s_decisions:
        min_evaluation_interval = 1.0   # 1 second for 1s decisions
        max_evaluation_interval = 10.0  # 10 seconds failsafe for 1s mode
    else:
        min_evaluation_interval = 5.0   # 5 seconds for 5m decisions
        max_evaluation_interval = 60.0  # 60 seconds failsafe for 5m mode

    last_evaluation_time = None  # Track last evaluation time
    previous_features = None     # Track previous features for smoothing
    feature_smoothing_factor = 0.3  # Smoothing factor (like live_trading.py)

    # ●●●●●●●● INDICATOR STALENESS SIMULATION (exactly like live_trading.py) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
    last_indicators_hash = None  # Track indicator changes
    last_5m_bar_time = None      # Track when we last got new 5m data
    stale_indicator_count = 0    # Count consecutive stale indicators

    # ●●●●●●●● SIGNAL STABILITY TRACKING (critical for realistic behavior) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
    signal_history = []          # Track recent signals for stability analysis
    last_entry_signal = 0.0      # Track previous entry signal

    # Signal smoothing configuration from config
    trade_params = config.get('tradeParams', {})
    use_smoothed_signals = trade_params.get('useSmoothedSignals', True)
    signal_smoothing_factor = trade_params.get('signalSmoothingFactor', 0.7)
    min_signal_stability_count = 1  # Require only 1 signal for triggering (reduced from 3)

    # ●●●●●●●● NEW ADAPTIVE SIGNAL PROCESSING ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
    # Initialize advanced signal smoother
    signal_smoothing_config = trade_params.get('signalSmoothing', {})
    smoothing_method = signal_smoothing_config.get('method', 'ema')

    # Remove method from config to avoid duplicate parameter
    smoother_config = signal_smoothing_config.copy()
    smoother_config.pop('method', None)

    signal_smoother = SignalSmoother(method=smoothing_method, **smoother_config)

    # Initialize adaptive threshold tuner
    threshold_tuner = create_threshold_tuner_from_config(config)

    # Threshold logging setup
    threshold_log_file = f"threshold_exceedance_{config['symbol']}_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.log"

    # Threshold evolution tracking for visualization
    threshold_evolution = {
        'timestamps': [],
        'long_thresholds': [],
        'short_thresholds': [],
        'exit_thresholds': [],
        'atr_values': [],
        'avg_atr_values': [],
        'volatility_ratios': [],
        'entry_signals': [],
        'exit_signals': [],
        'adaptive_long_thresholds': [],
        'adaptive_short_thresholds': [],
        'adaptive_exit_thresholds': []
    }

    log.info(f"🎛️ ADVANCED SIGNAL PROCESSING CONFIG:")
    log.info(f"   Use smoothed signals: {use_smoothed_signals}")
    log.info(f"   Legacy smoothing factor: {signal_smoothing_factor}")
    log.info(f"   Advanced smoothing method: {smoothing_method}")
    log.info(f"   Adaptive thresholds: {threshold_tuner.enabled}")
    log.info(f"   Threshold log file: {threshold_log_file}")
    log.info(f"   Signal processing: {'Advanced adaptive' if use_smoothed_signals else 'Raw signals'}")

    log.info(f"🎯 INTERVAL CONTROL SETUP:")
    log.info(f"   Min evaluation interval: {min_evaluation_interval}s")
    log.info(f"   Max evaluation interval: {max_evaluation_interval}s")
    log.info(f"   Use 1s decisions: {use_1s_decisions}")

    # --- Hlavná slučka ---
    log.info(f"DEBUG: Počet features: {num_features_from_data}, očakávaný flat size: {expected_flat_size_from_data}")
    log.info(f"DEBUG: Agent očakáva vstup tvaru: ({1}, {EXPECTED_AGENT_INPUT_SIZE}) -- Rozdiel: {EXPECTED_AGENT_INPUT_SIZE - expected_flat_size_from_data}")

    for i in range(lookback - 1, main_length):
        current_df_idx = i
        if use_1s_decisions:
            current_time = main_timestamps[current_df_idx]
        else:
            current_time = df_features.index[current_df_idx]

        # ●●●●●●●● SIMPLE TRACKING FOR EVERY STEP (for graph visualization) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
        # Track basic info for every step - entry_sig will be updated later when available
        threshold_evolution['timestamps'].append(current_time)
        threshold_evolution['entry_signals'].append(0.0)  # Will be updated later
        threshold_evolution['long_thresholds'].append(long_entry_thr)
        threshold_evolution['short_thresholds'].append(short_entry_thr)
        threshold_evolution['exit_thresholds'].append(exit_thr)
        threshold_evolution['atr_values'].append(current_atr)
        threshold_evolution['avg_atr_values'].append(current_atr)
        threshold_evolution['volatility_ratios'].append(1.0)
        threshold_evolution['exit_signals'].append(0.0)
        threshold_evolution['adaptive_long_thresholds'].append(long_entry_thr)
        threshold_evolution['adaptive_short_thresholds'].append(short_entry_thr)
        threshold_evolution['adaptive_exit_thresholds'].append(exit_thr)

        # Debug: Print tracking progress every 1000 steps
        if i % 1000 == 0:
            log.info(f"📊 Tracking progress: Step {i}, Total records: {len(threshold_evolution['timestamps'])}")

        # ●●●●●●●● ENHANCED INTERVAL CONTROL (exactly like live_trading.py) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
        # Check if enough time has passed since last evaluation
        if last_evaluation_time is not None:
            # Convert timestamps to comparable format
            if isinstance(current_time, pd.Timestamp):
                current_time_seconds = current_time.timestamp()
            else:
                current_time_seconds = pd.Timestamp(current_time).timestamp()

            if isinstance(last_evaluation_time, pd.Timestamp):
                last_eval_seconds = last_evaluation_time.timestamp()
            else:
                last_eval_seconds = pd.Timestamp(last_evaluation_time).timestamp()

            time_since_last_eval = current_time_seconds - last_eval_seconds

            # ●●●●●●●● ENHANCED THROTTLING + 5M DATA FRESHNESS (like live_trading.py) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
            # More aggressive throttling to match live trading behavior
            effective_min_interval = min_evaluation_interval

            # In 1s mode, be more selective about when to evaluate
            if use_1s_decisions:
                # Only evaluate if significant time has passed OR if we have a position
                if position == 0:
                    # No position: require longer intervals between evaluations
                    effective_min_interval = min_evaluation_interval * 3.0  # 3 seconds instead of 1 (more realistic)
                else:
                    # Have position: more frequent monitoring
                    effective_min_interval = min_evaluation_interval * 1.0  # 1 second with position

                # ●●●●●●●● REALISTIC 5M EVALUATION TIMING (exactly like live trading) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                # In live trading, we only evaluate when we get NEW 5m data
                # Since 1s injection doesn't work (1s data = 5m data), we need different approach

                # Only evaluate when we have NEW 5m indicators (hash changed)
                if not indicators_changed:
                    if i % 500 == 0:
                        log.debug(f"⏰ NO NEW 5M DATA: Indicators unchanged, skipping evaluation at {current_time}")
                    continue

                # Additional timing control: limit evaluation frequency even with new data
                if position == 0:
                    # No position: require longer intervals between evaluations (5+ minutes)
                    if time_since_last_eval < 300.0:  # 5 minutes
                        if i % 200 == 0:
                            log.debug(f"⏰ TIMING CONTROL: {time_since_last_eval:.1f}s < 300s, waiting for longer interval")
                        continue
                else:
                    # Have position: more frequent monitoring (1+ minute)
                    if time_since_last_eval < 60.0:  # 1 minute
                        if i % 100 == 0:
                            log.debug(f"⏰ TIMING CONTROL: {time_since_last_eval:.1f}s < 60s, waiting for position monitoring interval")
                        continue

            # Skip evaluation if not enough time has passed
            if time_since_last_eval < effective_min_interval:
                # Log throttling every 500 skipped iterations to avoid spam
                if i % 500 == 0:
                    log.debug(f"⏱️ THROTTLING: {time_since_last_eval:.1f}s < {effective_min_interval:.1f}s (pos={position}), skipping evaluation at {current_time}")
                continue

            # Log evaluation timing
            if i % 200 == 0 or time_since_last_eval >= max_evaluation_interval:
                log.info(f"🎯 EVALUATION: {time_since_last_eval:.1f}s since last, processing at {current_time} (pos={position})")

        # Update last evaluation time
        last_evaluation_time = current_time
        
        # Výpočet current_step_equity (s unrealized PnL)
        current_step_equity = equity_total
        current_unrealized_pnl = 0.0
        if position != 0:
            current_unrealized_pnl = position_size * (current_price_close - entry_price) * position
            current_step_equity += current_unrealized_pnl
        
        # Progress a PnL logging každých 100 krokov
        if i % 100 == 0:
            progress_pct = (i-lookback+1)/(main_length-lookback)*100 if main_length > lookback else 0
            total_pnl = current_step_equity - initial_equity
            log.info(f"🔄 Krok {i}/{main_length-1}: {current_time} | Progress: {progress_pct:.1f}% | Total PnL: ${total_pnl:.2f} | Equity: ${current_step_equity:.2f} | Pos: {position}")
        
        # --- Price data access: 1s mode uses 1s prices, 5m mode uses 5m prices ---
        if use_1s_decisions:
            try:
                current_row = data_1s.loc[current_time]
                # Log iba každých 500 sekúnd namiesto každej sekundy
                if i % 500 == 0:
                    log.debug(f"1s mode: using 1s OHLC for {current_time}")
            except KeyError:
                log.warning(f"Čas {current_time} nenájdený v '1s dátach'. Skip krok {i}."); continue
        else:
            try:
                current_row = data_5m.loc[current_time]
            except KeyError:
                log.warning(f"❌ Čas {current_time} nenájdený v 'data_5m'. Skip krok {i}."); continue

        current_price_close = current_row['close']; current_price_high = current_row['high']; current_price_low = current_row['low']

        # ●●●●●●●● INDICATOR CHANGE DETECTION (exactly like live_trading.py) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
        # Get current values for all key indicators from 5m data (source of indicators)
        if use_1s_decisions:
            # In 1s mode, get indicators from latest available 5m bar
            mask_5m_indicators = data_5m.index <= current_time
            available_5m_indicators = data_5m[mask_5m_indicators]
            if not available_5m_indicators.empty:
                indicator_row = available_5m_indicators.iloc[-1]
                current_5m_bar_time = available_5m_indicators.index[-1]
            else:
                indicator_row = pd.Series()  # Empty if no data
                current_5m_bar_time = None
        else:
            # In 5m mode, use current 5m bar
            indicator_row = current_row
            current_5m_bar_time = current_row.name if hasattr(current_row, 'name') else current_time

        # Calculate hash of key indicators to detect if they actually changed (like live_trading.py)
        key_indicators = ['close', 'RSI_14', 'EMA_21', 'ATR_14', 'SMA_50', 'MACD_12_26_9']
        available_indicators = [col for col in key_indicators if col in indicator_row.index]

        indicators_changed = False
        if len(available_indicators) > 0:
            # Get current indicator values
            current_values = []
            for col in available_indicators:
                val = indicator_row[col]
                current_values.append(f"{col}:{val:.8f}")

            current_indicators_hash = hash(str(current_values))

            # Check if indicators actually changed
            indicators_changed = (last_indicators_hash is None or
                                current_indicators_hash != last_indicators_hash)

            # Check if we got new 5m data
            new_5m_data = (last_5m_bar_time is None or
                          (current_5m_bar_time is not None and current_5m_bar_time > last_5m_bar_time))

            if indicators_changed and new_5m_data:
                log.info(f"✅ INDICATORS CHANGED: Hash {last_indicators_hash} → {current_indicators_hash}")
                log.info(f"✅ NEW 5M DATA: {last_5m_bar_time} → {current_5m_bar_time}")
                # Log key indicator values
                for col in available_indicators[:4]:  # Log first 4 to avoid spam
                    val = indicator_row[col]
                    log.info(f"   {col}: {val:.6f}")
                last_indicators_hash = current_indicators_hash
                last_5m_bar_time = current_5m_bar_time
                stale_indicator_count = 0
            elif not indicators_changed:
                stale_indicator_count += 1
                if i % 200 == 0:  # Log stale indicators occasionally
                    log.warning(f"⚠️ INDICATORS UNCHANGED: Hash {current_indicators_hash} (stale count: {stale_indicator_count})")
                    log.warning(f"   This simulates waiting for new 5m data in live trading")

                # ●●●●●●●● STALE INDICATOR PENALTY (simulate live trading delays) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                # In live trading, stale indicators mean we should wait longer or skip evaluation
                if stale_indicator_count > 10:  # After 10 consecutive stale readings
                    if i % 100 == 0:
                        log.info(f"⏳ STALE INDICATORS: Skipping evaluation due to {stale_indicator_count} stale readings")
                    continue  # Skip this evaluation to simulate waiting for fresh data

        # ●●●●●●●● COMPREHENSIVE INDICATOR LOGGING (exactly like live_trading.py) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
        # Log all key indicators every 50 steps or when significant changes occur
        if (i % 50 == 0 or position != 0 or indicators_changed) and len(available_indicators) > 0:

            # Define key indicators to log (exactly like live_trading.py)
            key_indicators = ['close', 'volume', 'RSI_14', 'EMA_9', 'EMA_21', 'SMA_50', 'ATR_14', 'ADX_14', 'DMP_14', 'DMN_14', 'MACD_12_26_9']
            available_indicators = [col for col in key_indicators if col in indicator_row.index]

            # Log to both main log and indicators file
            log.info(f"📊 INDICATORS @ {current_time}:")
            indicators_log.info(f"📊 INDICATORS @ {current_time}:")

            for feat in available_indicators:
                val = indicator_row[feat]
                if pd.notna(val):
                    log.info(f"   {feat}: {val:.6f}")
                    indicators_log.info(f"   {feat}: {val:.6f}")
                else:
                    log.warning(f"   {feat}: NaN")
                    indicators_log.warning(f"   {feat}: NaN")

            # Log current price and position info
            log.info(f"   Position: {position}, Price: ${current_price_close:.6f}")
            indicators_log.info(f"   Position: {position}, Price: ${current_price_close:.6f}")

            # Log recent price history (like live_trading.py)
            if use_1s_decisions and len(available_5m_indicators) >= 5:
                # Use 5m data for history in 1s mode
                recent_closes = available_5m_indicators['close'].tail(5).values if 'close' in available_5m_indicators.columns else []
                recent_rsi = available_5m_indicators['RSI_14'].tail(5).values if 'RSI_14' in available_5m_indicators.columns else []
                recent_ema = available_5m_indicators['EMA_21'].tail(5).values if 'EMA_21' in available_5m_indicators.columns else []
                recent_atr = available_5m_indicators['ATR_14'].tail(5).values if 'ATR_14' in available_5m_indicators.columns else []
            elif not use_1s_decisions and len(data_5m) >= 5:
                # Use current data for history in 5m mode
                current_5m_idx = data_5m.index.get_loc(current_time) if current_time in data_5m.index else -1
                if current_5m_idx >= 4:
                    history_start = current_5m_idx - 4
                    history_end = current_5m_idx + 1
                    recent_closes = data_5m['close'].iloc[history_start:history_end].values if 'close' in data_5m.columns else []
                    recent_rsi = data_5m['RSI_14'].iloc[history_start:history_end].values if 'RSI_14' in data_5m.columns else []
                    recent_ema = data_5m['EMA_21'].iloc[history_start:history_end].values if 'EMA_21' in data_5m.columns else []
                    recent_atr = data_5m['ATR_14'].iloc[history_start:history_end].values if 'ATR_14' in data_5m.columns else []
                else:
                    recent_closes = recent_rsi = recent_ema = recent_atr = []
            else:
                recent_closes = recent_rsi = recent_ema = recent_atr = []

            if len(recent_closes) > 0:
                log.info(f"🔍 Last {len(recent_closes)} closes: {recent_closes}")
                indicators_log.info(f"🔍 Last {len(recent_closes)} closes: {recent_closes}")
            if len(recent_rsi) > 0:
                log.info(f"🔍 Last {len(recent_rsi)} RSI values: {recent_rsi}")
                indicators_log.info(f"🔍 Last {len(recent_rsi)} RSI values: {recent_rsi}")
            if len(recent_ema) > 0:
                log.info(f"🔍 Last {len(recent_ema)} EMA values: {recent_ema}")
                indicators_log.info(f"🔍 Last {len(recent_ema)} EMA values: {recent_ema}")
            if len(recent_atr) > 0:
                log.info(f"🔍 Last {len(recent_atr)} ATR values: {recent_atr}")
                indicators_log.info(f"🔍 Last {len(recent_atr)} ATR values: {recent_atr}")

        # --- ATR access: always from 5m data, even in 1s mode ---
        if use_1s_decisions and atr_col:
            # In 1s mode, find corresponding 5m bar for ATR (latest available without look-ahead)
            mask_5m_atr = data_5m.index <= current_time
            available_5m_atr = data_5m[mask_5m_atr]
            if not available_5m_atr.empty:
                current_atr = available_5m_atr.iloc[-1].get(atr_col, 0.0)
                current_atr = current_atr if pd.notna(current_atr) else 0.0
                # Log iba každých 1000 sekúnd namiesto každej sekundy
                if i % 1000 == 0:
                    log.debug(f"1s mode: ATR {current_atr:.6f} from 5m bar {available_5m_atr.index[-1]}")
            else:
                current_atr = 0.0
        else:
            # Standard 5m mode or no ATR column
            current_atr = current_row.get(atr_col, 0.0) if atr_col else 0.0
            current_atr = current_atr if pd.notna(current_atr) else 0.0

        # Update ATR history for adaptive smoothing
        if current_atr > 0:
            atr_history.append(current_atr)
            if len(atr_history) > 100:  # Keep last 100 ATR values
                atr_history.pop(0)

        # --- ATR checking VYPNUTÉ - agent má plnú kontrolu ---
        is_atr_valid_for_calc = True  # Vždy True - agent rozhoduje o všetkom
        # Pôvodný ATR checking je vypnutý kvôli požiadavke na 100% agent control

        # Dynamic threshold adjustment based on volatility
        if enable_dynamic_thresholds and current_atr > 0:
            # Calculate average ATR over last 20 periods for comparison
            if use_1s_decisions and atr_col:
                mask_5m_atr_avg = data_5m.index <= current_time
                available_5m_atr_avg = data_5m[mask_5m_atr_avg]
                if len(available_5m_atr_avg) >= 20:
                    avg_atr = available_5m_atr_avg[atr_col].tail(20).mean()
                else:
                    avg_atr = current_atr  # Fallback to current ATR
            else:
                # For 5m mode, calculate rolling average
                if i >= 20:
                    avg_atr = df_features[atr_col].iloc[i-19:i+1].mean() if atr_col else current_atr
                else:
                    avg_atr = current_atr

            # Apply dynamic threshold adjustment
            long_entry_thr, short_entry_thr, exit_thr = calculate_dynamic_thresholds(
                current_atr, avg_atr, base_long_entry_thr, base_short_entry_thr,
                base_exit_thr, volatility_adjustment_factor
            )

            # ●●●●●●●● DYNAMIC THRESHOLD LOGGING (exactly like live_trading.py) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
            if i % 500 == 0:  # Log more frequently for better visibility
                log.info(f"📊 DYNAMIC THRESHOLDS: Long={long_entry_thr:.3f}, Short={short_entry_thr:.3f}, Exit={exit_thr:.3f}")
                log.info(f"📊 ATR STATUS: Current={current_atr:.6f}, Average={avg_atr:.6f}, Adjustment={volatility_adjustment_factor}")
                log.info(f"📊 BASE THRESHOLDS: Long={base_long_entry_thr:.3f}, Short={base_short_entry_thr:.3f}, Exit={base_exit_thr:.3f}")
        else:
            # Use base thresholds
            long_entry_thr = base_long_entry_thr
            short_entry_thr = base_short_entry_thr
            exit_thr = base_exit_thr

        # --- Intra-bar 1s SL/TP checking (iba v 5m mode) ---
        if not use_1s_decisions and position != 0 and has_1s_data and current_time < data_5m.index[-1]:
            try:
                # V 5m mode: kontrolujeme SL/TP na 1s dátach medzi 5m barmi
                next_time = data_5m.index[current_df_idx + 1] if current_df_idx + 1 < len(data_5m) else data_5m.index[-1]
                
                # Kontrola dostupnosti 1s dát v požadovanom rozsahu
                if current_time in data_1s.index and next_time <= data_1s.index.max():
                    second_data_slice = data_1s.loc[current_time:next_time].iloc[1:] # Vynecháme prvý bar, ktorý je zhodný s aktuálnym 5m barom

                    if not second_data_slice.empty:
                        log.debug(f"5m mode: Kontrolujem {len(second_data_slice)} 1s barov medzi {current_time} a {next_time}")

                        # Prejdeme všetky 1s bary a skontrolujeme SL/TP
                        for sec_idx, sec_row in second_data_slice.iterrows():
                            sec_high = sec_row['high']; sec_low = sec_row['low'] # sec_close = sec_row['close'] - nepoužívame

                            # Kontrola SL/TP/TSL na 1s dátach
                            if position == 1: # Long
                                # SL/TP priority logic like in training env
                                hit_sl_1s = current_sl_price > 0 and sec_low <= current_sl_price
                                hit_tp_1s = current_tp_price > 0 and sec_high >= current_tp_price
                                # SL has priority over TP
                                if hit_sl_1s and hit_tp_1s: hit_tp_1s = False
                                
                                if hit_sl_1s:
                                    exit_reason = "SL"; exit_price = current_sl_price
                                    log.debug(f"SL LONG aktivovaný na 1s bare {sec_idx}")
                                    break
                                elif hit_tp_1s:
                                    exit_reason = "TP"; exit_price = current_tp_price
                                    log.debug(f"TP LONG aktivovaný na 1s bare {sec_idx}")
                                    break

                                # Update TSL Long na 1s dátach (FIXED VERSION - using original SL)
                                if tsl_enabled and current_atr > 0:
                                    # R-multiple based activation using original SL distance
                                    original_sl_distance = abs(entry_price - original_sl_price) if 'original_sl_price' in locals() and original_sl_price > 0 else abs(entry_price - current_sl_price)
                                    profit = sec_high - entry_price
                                    r_mult = profit / original_sl_distance if original_sl_distance > 1e-9 else 0

                                    # DEBUG: Log profit progress every 50 bars in intra-bar checking
                                    profit_percent = (profit / entry_price) * 100 if entry_price > 0 else 0
                                    if (sec_idx.minute * 60 + sec_idx.second) % 50 == 0:
                                        log.info(f"🔍 TSL LONG INTRA-BAR @ {sec_idx}: Profit={profit_percent:.3f}%/{tsl_activate_percent:.1f}% | Profit=${profit:.5f} | Entry=${entry_price:.5f}")

                                    # Activate trailing when profit reaches tsl_activate_percent %
                                    if not trailing_active and profit_percent >= tsl_activate_percent:
                                        trailing_active = True
                                        trail_distance = entry_price * (tsl_trail_percent / 100)
                                        new_tsl_sl = sec_high - trail_distance
                                        old_sl = current_sl_price

                                        # Use more reasonable minimum distance (0.05% of entry price)
                                        min_sl_distance = entry_price * 0.0005
                                        min_allowed_sl = entry_price - min_sl_distance

                                        current_sl_price = max(new_tsl_sl, min_allowed_sl)
                                        peak_price_in_trade = sec_high
                                        log.info(f"🎯 TSL LONG ACTIVATED @ {sec_idx}: Profit={profit_percent:.2f}% | Peak={sec_high:.5f} | SL: {old_sl:.5f} → {current_sl_price:.5f} | Trail={tsl_trail_percent:.2f}%")
                                    elif trailing_active:
                                        # Update peak first - CRITICAL FIX
                                        if sec_high > peak_price_in_trade:
                                            peak_price_in_trade = sec_high
                                            # Only calculate new SL based on improved peak
                                            trail_distance = entry_price * (tsl_trail_percent / 100)
                                            new_sl = peak_price_in_trade - trail_distance
                                            
                                            # Use same minimum distance logic
                                            min_sl_distance = entry_price * 0.0005
                                            min_allowed_sl = entry_price - min_sl_distance

                                            # Only update if new SL is higher (tighter)
                                            if new_sl > current_sl_price:
                                                old_sl = current_sl_price
                                                current_sl_price = max(new_sl, min_allowed_sl)
                                                sl_movement = current_sl_price - old_sl
                                                log.info(f"📈 TSL LONG MOVED @ {sec_idx}: Peak={peak_price_in_trade:.5f} | SL: {old_sl:.5f} → {current_sl_price:.5f} | Move=+{sl_movement:.5f}")

                            elif position == -1: # Short
                                # SL/TP priority logic like in training env
                                hit_sl_1s = current_sl_price > 0 and sec_high >= current_sl_price
                                hit_tp_1s = current_tp_price > 0 and sec_low <= current_tp_price
                                # SL has priority over TP
                                if hit_sl_1s and hit_tp_1s: hit_tp_1s = False
                                
                                if hit_sl_1s:
                                    exit_reason = "SL"; exit_price = current_sl_price
                                    log.debug(f"SL SHORT aktivovaný na 1s bare {sec_idx}")
                                    break
                                elif hit_tp_1s:
                                    exit_reason = "TP"; exit_price = current_tp_price
                                    log.debug(f"TP SHORT aktivovaný na 1s bare {sec_idx}")
                                    break

                                # Update TSL Short na 1s dátach (FIXED VERSION - using original SL)
                                if tsl_enabled and current_atr > 0:
                                    # R-multiple based activation using original SL distance
                                    original_sl_distance = abs(entry_price - original_sl_price) if 'original_sl_price' in locals() and original_sl_price > 0 else abs(entry_price - current_sl_price)
                                    profit = entry_price - sec_low
                                    r_mult = profit / original_sl_distance if original_sl_distance > 1e-9 else 0

                                    # DEBUG: Log profit progress every 50 bars in intra-bar checking
                                    profit_percent = (profit / entry_price) * 100 if entry_price > 0 else 0
                                    if (sec_idx.minute * 60 + sec_idx.second) % 50 == 0:
                                        log.info(f"🔍 TSL SHORT INTRA-BAR @ {sec_idx}: Profit={profit_percent:.3f}%/{tsl_activate_percent:.1f}% | Profit=${profit:.5f} | Entry=${entry_price:.5f}")

                                    # Activate trailing when profit reaches tsl_activate_percent %
                                    if not trailing_active and profit_percent >= tsl_activate_percent:
                                        trailing_active = True
                                        peak_price_in_trade = sec_low  # Track lowest price reached
                                        old_sl = current_sl_price
                                        trail_distance = entry_price * (tsl_trail_percent / 100)
                                        current_sl_price = peak_price_in_trade + trail_distance
                                        log.info(f"🎯 TSL SHORT ACTIVATED @ {sec_idx}: Profit={profit_percent:.2f}% | Peak={sec_low:.5f} | SL: {old_sl:.5f} → {current_sl_price:.5f} | Trail={tsl_trail_percent:.2f}%")
                                    elif trailing_active:
                                        # Update peak (lowest price for SHORT)
                                        if sec_low < peak_price_in_trade:
                                            peak_price_in_trade = sec_low
                                            trail_distance = entry_price * (tsl_trail_percent / 100)
                                            new_sl = peak_price_in_trade + trail_distance
                                            # Only move SL down (tighter) for SHORT, never up (looser)
                                            if new_sl < current_sl_price:
                                                old_sl = current_sl_price
                                                current_sl_price = new_sl
                                                sl_movement = old_sl - current_sl_price
                                                log.info(f"📉 TSL SHORT MOVED @ {sec_idx}: Peak={peak_price_in_trade:.5f} | SL: {old_sl:.5f} → {current_sl_price:.5f} | Move=-{sl_movement:.5f}")
                else:
                    # 1s dáta nie sú dostupné v tomto rozsahu - pokračuj bez intra-bar checking
                    if i % 500 == 0:  # Log iba občas, aby sme nespamovali
                        log.info(f"{current_time}: 1s dáta nedostupné pre intra-bar SL/TP checking (pokračuje bez neho)")
                        
            except Exception as e:
                # Ak nastane chyba pri prístupe k 1s dátam, pokračuj bez intra-bar checking
                if i % 500 == 0:  # Log iba občas
                    log.warning(f"{current_time}: Chyba pri intra-bar 1s checking: {e} (pokračuje bez neho)")
        elif use_1s_decisions:
            # V 1s mode: SL/TP sa kontroluje priamo v hlavnej slučke, intra-bar checking nie je potrebný
            # Log iba raz za simuláciu namiesto každej sekundy
            if i == 0:
                log.debug(f"1s mode: SL/TP checking will be done in main loop, no intra-bar needed")

        # >>> NOVÉ: Daily risk management reset (ako v ScalpingEnv)
        cur_day = current_time.date()
        if last_day is None or cur_day != last_day:
            daily_pnl = 0.0
            daily_risk_used = 0.0
            daily_trade_count = 0
            trading_allowed = True
            last_day = cur_day
            log.debug(f"{current_time}: Nový deň, reset daily limits")

        # Stop trading if daily limits exceeded (configurable)
        risk_mgmt = config.get('riskManagement', {})
        max_daily_risk_r_mult = risk_mgmt.get('maxDailyRiskPercent', 999999.0)  # R-multiple limit (positive value)
        max_daily_trades = config.get('maxDailyTrades', 999999)  # Very high default for debug
        max_daily_atr_loss_multiplier = config.get('maxDailyAtrLossMultiplier', 999999.0)  # Very high default

        # Check if daily loss limits exceeded (negative R-multiple means loss)
        if (daily_risk_used <= -max_daily_risk_r_mult or
            (current_atr > 0 and daily_pnl <= -max_daily_atr_loss_multiplier * current_atr) or
            daily_trade_count >= max_daily_trades) and trading_allowed:
            trading_allowed = False
            log.warning(f"{current_time}: Daily limit hit - risk: {daily_risk_used:.2f}R (limit: -{max_daily_risk_r_mult:.2f}R), pnl: {daily_pnl:.2f}, trades: {daily_trade_count} (limit: {max_daily_trades})")
        
        # Daily PnL tracking (s unrealized PnL)
        current_daily_pnl = daily_pnl + current_unrealized_pnl  # Realized + Unrealized PnL
        
        # Debug daily status
        if i % 500 == 0:
            log.info(f"{current_time}: Daily status - trading_allowed: {trading_allowed}, risk_used: {daily_risk_used:.2f}, realized_pnl: {daily_pnl:.2f}, unrealized_pnl: {current_unrealized_pnl:.2f}, total_daily_pnl: {current_daily_pnl:.2f}, trades: {daily_trade_count}")

        # --- Equity Curve ---
        current_step_equity = equity_total
        if position != 0:
            unrealized_pnl = position_size * (current_price_close - entry_price) * position
            current_step_equity += unrealized_pnl
        equity_curve.append({'timestamp': current_time, 'equity': current_step_equity})
        
        # Update max equity
        max_equity = max(max_equity, current_step_equity)

        exit_reason = None; exit_price = 0.0; action_raw = np.zeros(4)

        # --- Cooldown ---
        if cooldown_counter > 0:
            cooldown_counter -= 1
            # Skip all trading logic if in cooldown
            if cooldown_counter > 0:
                continue

        # --- Kontrola SL/TP/TSL (EXACTLY as in ScalpingEnv) ---
        if position == 1: # Long
            hit_sl = current_sl_price > 0 and current_price_low <= current_sl_price
            hit_tp = current_tp_price > 0 and current_price_high >= current_tp_price
            # SL has priority over TP (exactly like in env)
            if hit_sl and hit_tp: hit_tp = False

            # Check minimum time in trade before allowing SL/TP exit
            time_in_trade_seconds = (current_df_idx - open_idx_sim) * (60 if not use_1s_decisions else 1)
            if time_in_trade_seconds < min_time_in_trade:
                log.debug(f"⏰ SL/TP blocked: time_in_trade={time_in_trade_seconds}s < min_required={min_time_in_trade}s")
                hit_sl = hit_tp = False

            if hit_sl: exit_reason = "SL"; exit_price = current_sl_price
            elif hit_tp: exit_reason = "TP"; exit_price = current_tp_price

            # Update TSL Long (FIXED VERSION - using original SL for R-multiple)
            if exit_reason is None and tsl_enabled and current_atr > 0:
                # R-multiple based activation using ORIGINAL SL distance
                original_sl_distance = abs(entry_price - original_sl_price) if 'original_sl_price' in locals() and original_sl_price > 0 else abs(entry_price - current_sl_price)
                profit = current_price_high - entry_price
                r_mult = profit / original_sl_distance if original_sl_distance > 1e-9 else 0

                # DEBUG: Log profit progress every 100 steps for active positions
                profit_percent = (profit / entry_price) * 100 if entry_price > 0 else 0
                if i % 100 == 0:
                    log.info(f"🔍 TSL LONG DEBUG @ {current_time}: Profit={profit_percent:.3f}%/{tsl_activate_percent:.1f}% | Profit=${profit:.5f} | Entry=${entry_price:.5f} | Peak={current_price_high:.5f}")

                # Activate trailing when profit reaches tsl_activate_percent %
                if not trailing_active and profit_percent >= tsl_activate_percent:
                    trailing_active = True
                    trail_distance = entry_price * (tsl_trail_percent / 100)
                    new_tsl_sl = current_price_high - trail_distance
                    old_sl = current_sl_price

                    # Use more reasonable minimum distance (0.05% of entry price)
                    min_sl_distance = entry_price * 0.0005
                    min_allowed_sl = entry_price - min_sl_distance

                    current_sl_price = max(new_tsl_sl, min_allowed_sl)
                    peak_price_in_trade = current_price_high
                    log.info(f"🎯 TSL LONG ACTIVATED @ {current_time}: R={r_mult:.2f} | Peak={current_price_high:.5f} | SL: {old_sl:.5f} → {current_sl_price:.5f} | ATR={current_atr:.5f}")
                elif trailing_active:
                    # Update peak first - CRITICAL FIX
                    if current_price_high > peak_price_in_trade:
                        peak_price_in_trade = current_price_high
                        # Only calculate new SL based on improved peak
                        trail_distance = entry_price * (tsl_trail_percent / 100)
                        new_sl = peak_price_in_trade - trail_distance
                        
                        # Use same minimum distance logic
                        min_sl_distance = entry_price * 0.0005
                        min_allowed_sl = entry_price - min_sl_distance

                        # Only update if new SL is higher (tighter)
                        if new_sl > current_sl_price:
                            old_sl = current_sl_price
                            current_sl_price = max(new_sl, min_allowed_sl)
                            sl_movement = current_sl_price - old_sl
                            log.info(f"📈 TSL LONG MOVED @ {current_time}: Peak={peak_price_in_trade:.5f} | SL: {old_sl:.5f} → {current_sl_price:.5f} | Move=+{sl_movement:.5f}")

        elif position == -1: # Short
            hit_sl = current_sl_price > 0 and current_price_high >= current_sl_price
            hit_tp = current_tp_price > 0 and current_price_low <= current_tp_price
            # SL has priority over TP (exactly like in env)
            if hit_sl and hit_tp: hit_tp = False

            # Check minimum time in trade before allowing SL/TP exit
            time_in_trade_seconds = (current_df_idx - open_idx_sim) * (60 if not use_1s_decisions else 1)
            if time_in_trade_seconds < min_time_in_trade:
                log.debug(f"⏰ SL/TP blocked: time_in_trade={time_in_trade_seconds}s < min_required={min_time_in_trade}s")
                hit_sl = hit_tp = False

            if hit_sl: exit_reason = "SL"; exit_price = current_sl_price
            elif hit_tp: exit_reason = "TP"; exit_price = current_tp_price

            # Update TSL Short (FIXED VERSION - using original SL for R-multiple)
            if exit_reason is None and tsl_enabled and current_atr > 0:
                 # R-multiple based activation using ORIGINAL SL distance
                 original_sl_distance = abs(entry_price - original_sl_price) if 'original_sl_price' in locals() and original_sl_price > 0 else abs(entry_price - current_sl_price)
                 profit = entry_price - current_price_low
                 r_mult = profit / original_sl_distance if original_sl_distance > 1e-9 else 0

                 # DEBUG: Log profit progress every 100 steps for active positions
                 profit_percent = (profit / entry_price) * 100 if entry_price > 0 else 0
                 if i % 100 == 0:
                     log.info(f"🔍 TSL SHORT DEBUG @ {current_time}: Profit={profit_percent:.3f}%/{tsl_activate_percent:.1f}% | Profit=${profit:.5f} | Entry=${entry_price:.5f} | Peak={current_price_low:.5f}")

                 # Activate trailing when profit reaches tsl_activate_percent %
                 if not trailing_active and profit_percent >= tsl_activate_percent:
                     trailing_active = True
                     peak_price_in_trade = current_price_low  # Track lowest price reached
                     old_sl = current_sl_price

                     # Use more reasonable minimum distance (0.05% of entry price above entry)
                     min_sl_distance = entry_price * 0.0005
                     max_allowed_sl = entry_price + min_sl_distance

                     trail_distance = entry_price * (tsl_trail_percent / 100)
                     new_tsl_sl = peak_price_in_trade + trail_distance
                     current_sl_price = min(new_tsl_sl, max_allowed_sl)
                     log.info(f"🎯 TSL SHORT ACTIVATED @ {current_time}: R={r_mult:.2f} | Peak={current_price_low:.5f} | SL: {old_sl:.5f} → {current_sl_price:.5f} | ATR={current_atr:.5f}")
                 elif trailing_active:
                     # Update peak (lowest price for SHORT)
                     if current_price_low < peak_price_in_trade:
                         peak_price_in_trade = current_price_low
                         trail_distance = entry_price * (tsl_trail_percent / 100)
                         new_sl = peak_price_in_trade + trail_distance

                         # Use same minimum distance logic
                         min_sl_distance = entry_price * 0.0005
                         max_allowed_sl = entry_price + min_sl_distance

                         # Only move SL down (tighter) for SHORT, never up (looser)
                         if new_sl < current_sl_price:
                             old_sl = current_sl_price
                             current_sl_price = min(new_sl, max_allowed_sl)
                             sl_movement = old_sl - current_sl_price
                             log.info(f"📉 TSL SHORT MOVED @ {current_time}: Peak={peak_price_in_trade:.5f} | SL: {old_sl:.5f} → {current_sl_price:.5f} | Move=-{sl_movement:.5f}")
            


        # --- Ak nastal SL/TP/TSL exit ---
        if exit_reason:
            sim_exit_price = exit_price
            sim_exit_price = sim_exit_price * (1 - slippage_perc * position) # Aplikuj slippage
            log.info(f"{current_time}: EXIT {position_size:.4f} u {'L' if position == 1 else 'S'} @ {sim_exit_price:.5f} (R: {exit_reason}, Trig: {exit_price:.5f})")
            pnl = position_size * (sim_exit_price - entry_price) * position
            exit_fee = abs(position_size * sim_exit_price * fee_perc)
            net_pnl = pnl - entry_fee - exit_fee
            
            # DEBUG: Detailný PnL breakdown
            equity_before = equity_total
            equity_total += net_pnl  # FIXED: Use net_pnl which properly accounts for both fees
            equity_change = equity_total - equity_before
            log.info(f"  💰 PnL BREAKDOWN: gross_pnl={pnl:.6f}, entry_fee={entry_fee:.6f}, exit_fee={exit_fee:.6f}")
            log.info(f"  💰 NET_PnL={net_pnl:.6f}, EQUITY_CHANGE={equity_change:.6f}, EQ: {equity_before:.2f} → {equity_total:.2f}")
            
            # Daily risk tracking (using original SL for accurate R-multiple)
            original_sl_distance = abs(entry_price - original_sl_price) if 'original_sl_price' in locals() and original_sl_price > 0 else abs(entry_price - current_sl_price)
            r_mult = net_pnl / (original_sl_distance * position_size) if original_sl_distance > 1e-9 else 0
            daily_risk_used += r_mult
            daily_pnl += net_pnl
            
            trades.append({
                'entry_time': entry_time, 'exit_time': current_time,
                'direction': 'Long' if position == 1 else 'Short',
                'size': position_size, 'entry_price': entry_price, 'exit_price': sim_exit_price,
                'pnl': net_pnl, 'exit_reason': exit_reason, 'entry_fee': entry_fee, 'exit_fee': exit_fee
            })
            # PnL info is logged in detailed exit statement above
            
            # >>> NOVÉ: Cooldown after SL (ako v ScalpingEnv)
            if exit_reason.startswith("SL"):
                cooldown_counter = 60  # 60 second cooldown after stop loss
                log.debug(f"SL hit, starting cooldown: {cooldown_counter}s")
            elif exit_reason == "TP":
                cooldown_counter = 3  # Short cooldown after TP
            
            # Reset stavu
            position = 0; position_size = 0.0; entry_price = 0.0; entry_time = None; entry_fee = 0.0
            current_sl_price = 0.0; original_sl_price = 0.0; current_tp_price = 0.0; current_tsl_price = 0.0; peak_price_in_trade = 0.0
            trailing_active = False; entry_atr = 0.0; open_idx_sim = -1


        # --- OPTIMALIZÁCIA: Počas obchodu len basic processing, bez agenta ---
        in_active_trade = position != 0
        
        # --- Získanie a interpretácia akcie agenta (ak sme stále v pozícii alebo flat) ---
        if exit_reason is None:
            if in_active_trade:
                # 🚀 OPTIMALIZOVANÉ SPRACOVANIE PRE AKTÍVNE OBCHODY
                # Počas obchodu: iba exit signály, preskačujeme komplexné state calculation
                try:
                    # Jednoduchý fake action pre exit signal checking
                    # Môžeme použiť posledný známy exit signal alebo default
                    entry_sig = 0.0  # Nepoužíva sa počas obchodu
                    exit_sig = 0.0   # Default: žiadny exit signal
                    a_tp = 0.0       # Nepoužíva sa počas obchodu
                    
                    # Iba ak máme povolené agent exits, spustíme predict
                    if agent_exits_enabled:
                        # Minimálne state calculation pre exit decisions
                        state_vector, _ = get_state(
                            df_features=df_features,
                            current_step_index=current_df_idx,
                            lookback=lookback,
                            current_pos=position,
                            entry_price=entry_price,
                            current_price=current_price_close,
                            current_sl_price=current_sl_price,
                            current_tp_price=current_tp_price,
                            open_idx_sim=open_idx_sim,
                            current_atr=current_atr if is_atr_valid_for_calc else 0.0,
                            inactivity_limit=inactivity_limit,
                            tsl_enabled=tsl_enabled,
                            use_1s_decisions=use_1s_decisions,
                            features_5m=features_5m,
                            main_timestamps=main_timestamps,
                            current_time=current_time,
                            previous_features=previous_features,
                            smoothing_factor=feature_smoothing_factor,
                            data_1s=data_1s
                        )
                        
                        state_input = np.expand_dims(state_vector, axis=0)
                        
                        # Aplikuj VecNormalize ak je dostupné
                        if vecnorm is not None:
                            state_input = vecnorm.normalize_obs(state_input)
                        
                        state_clipped = np.clip(state_input, agent_obs_low, agent_obs_high).astype(np.float32)
                        
                        # REMOVED: Per-step RNG seeding - using global seed only
                        # Let the model use its natural stochastic behavior without per-step reseeding

                        # STOCHASTIC predict for exit signal (natural model behavior)
                        action_raw, _ = agent.predict(state_clipped, deterministic=False)
                        if isinstance(action_raw, np.ndarray) and action_raw.size >= 4:
                            action_clipped = np.clip(action_raw.flatten(), -1.0, 1.0)
                            exit_sig = action_clipped[3]
                        
                        # Log iba občas pri aktívnom obchode
                        if i % 1000 == 0:
                            log.debug(f"{current_time}: 🏃 FAST MODE - Active trade, exit_sig={exit_sig:.3f}")
                    else:
                        # Agent exits vypnuté - preskačujeme úplne
                        if i % 1000 == 0:
                            log.debug(f"{current_time}: 🏃 FAST MODE - Agent exits disabled, SL/TP only")
                        
                except Exception as e:
                    log.warning(f"Chyba v fast mode pre aktívny obchod v kroku {i}: {e}")
                    # Fallback na default values
                    entry_sig = 0.0; exit_sig = 0.0; a_tp = 0.0
                    
            else:
                # 🤖 ŠTANDARDNÉ SPRACOVANIE PRE FLAT POZÍCIE
                try:
                     # DEBUG: Spustil sa krok simulácie
                     if i <= 3:
                         log.info(f"{current_time}: 🔄 SIMULATION_STEP: {i} - Starting processing...")
                     
                     # Real-time forward-fill volanie pre 1s mode alebo štandardné pre 5m mode
                     state_vector, current_features = get_state(
                         df_features=df_features,
                         current_step_index=current_df_idx,
                         lookback=lookback,
                         current_pos=position,
                         entry_price=entry_price,
                         current_price=current_price_close,
                         current_sl_price=current_sl_price,
                         current_tp_price=current_tp_price,
                         open_idx_sim=open_idx_sim,
                         current_atr=current_atr if is_atr_valid_for_calc else 0.0,
                         inactivity_limit=inactivity_limit,
                         tsl_enabled=tsl_enabled,
                         use_1s_decisions=use_1s_decisions,
                         features_5m=features_5m,
                         main_timestamps=main_timestamps,
                         current_time=current_time,
                         previous_features=previous_features,
                         smoothing_factor=feature_smoothing_factor,
                         data_1s=data_1s
                     )

                     # Update previous_features for next iteration smoothing
                     if current_features is not None:
                         previous_features = current_features

                     state_input = np.expand_dims(state_vector, axis=0)
                     
                     # ●●●●●●●● VECNORMALIZE DEBUGGING (critical for identical signal issue) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                     # Aplikuj VecNormalize ak je dostupné
                     if vecnorm is not None:
                         try:
                             state_before_norm = state_input.copy()
                             state_input = vecnorm.normalize_obs(state_input)

                             # Debug normalization for first few steps
                             if i <= 3:
                                 norm_effect = np.mean(np.abs(state_input - state_before_norm))
                                 log.info(f"🔧 VECNORMALIZE @ {current_time}:")
                                 log.info(f"   Before norm: mean={np.mean(state_before_norm):.8f}, std={np.std(state_before_norm):.8f}")
                                 log.info(f"   After norm: mean={np.mean(state_input):.8f}, std={np.std(state_input):.8f}")
                                 log.info(f"   Normalization effect: {norm_effect:.8f}")

                                 # Check if normalization is "crushing" all differences
                                 if norm_effect < 1e-6:
                                     log.error(f"   ❌ NORMALIZATION CRUSHING DIFFERENCES! Effect too small: {norm_effect:.12f}")

                                 # Check VecNormalize statistics
                                 if hasattr(vecnorm.obs_rms, 'mean') and hasattr(vecnorm.obs_rms, 'var'):
                                     log.info(f"   VecNorm mean range: [{np.min(vecnorm.obs_rms.mean):.6f}, {np.max(vecnorm.obs_rms.mean):.6f}]")
                                     log.info(f"   VecNorm var range: [{np.min(vecnorm.obs_rms.var):.6f}, {np.max(vecnorm.obs_rms.var):.6f}]")

                                     # Check for problematic normalization values
                                     zero_var_count = np.sum(vecnorm.obs_rms.var < 1e-8)
                                     if zero_var_count > 0:
                                         log.error(f"   ❌ ZERO VARIANCE FEATURES: {zero_var_count} features have near-zero variance!")
                                         log.error(f"   ❌ This will cause identical normalized values regardless of input!")

                             # Log iba každých 2000 sekúnd namiesto každej sekundy
                             elif i % 2000 == 0:
                                 log.debug(f"{current_time}: VecNormalize aplikované na state")
                         except Exception as e:
                             log.warning(f"{current_time}: Chyba pri aplikácii VecNormalize: {e}")
                     else:
                         if i <= 3:
                             log.info(f"🔧 NO VECNORMALIZE @ {current_time}: Using raw state without normalization")
                     
                     state_clipped = np.clip(state_input, agent_obs_low, agent_obs_high).astype(np.float32)

                     # Kontrola tvaru PRED volaním predict
                     if state_clipped.shape != (1, EXPECTED_AGENT_INPUT_SIZE):
                         log.error(f"NEZHODA TVARU PRED PREDICT! Očakávaný: {(1, EXPECTED_AGENT_INPUT_SIZE)}, Aktuálny: {state_clipped.shape}! Skip krok.")
                         continue



                     # ●●●●●●●● ENHANCED STATE DEBUGGING (debug constant signals) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                     state_hash = hash(state_clipped.tobytes())

                     # Log detailed state analysis for first 20 steps to debug constant signals
                     if i <= 20:  # Extended debugging for constant signal issue
                         state_summary = {
                             'timestamp': current_time,
                             'step': i,
                             'state_hash': state_hash,
                             'position': position,
                             'price': current_price_close,
                             'state_shape': state_clipped.shape,
                             'state_mean': float(np.mean(state_clipped)),
                             'state_std': float(np.std(state_clipped)),
                             'state_min': float(np.min(state_clipped)),
                             'state_max': float(np.max(state_clipped))
                         }

                         log.info(f"🔍 STATE DEBUG @ {current_time}:")
                         log.info(f"   Hash: {state_hash}")
                         log.info(f"   Shape: {state_summary['state_shape']}")
                         log.info(f"   Stats: mean={state_summary['state_mean']:.8f}, std={state_summary['state_std']:.8f}")
                         log.info(f"   Range: min={state_summary['state_min']:.8f}, max={state_summary['state_max']:.8f}")
                         log.info(f"   Price: {current_price_close:.8f}")

                         # Log first 10 and last 10 elements of state vector for analysis
                         state_flat = state_clipped.flatten()
                         log.info(f"   First 10 elements: {state_flat[:10]}")
                         log.info(f"   Last 10 elements: {state_flat[-10:]}")

                         # Check if state is changing between steps
                         if i > 0 and hasattr(run_backtest_original, '_last_state_hash'):
                             if state_hash == run_backtest_original._last_state_hash:
                                 log.error(f"❌ IDENTICAL STATE: Step {i} has SAME state as previous step!")
                                 log.error(f"   This explains constant entry_sig! State is not changing!")

                                 # Log what should be changing
                                 if hasattr(run_backtest_original, '_last_price'):
                                     price_change = current_price_close - run_backtest_original._last_price
                                     log.error(f"   Price change: {run_backtest_original._last_price:.8f} → {current_price_close:.8f} (Δ={price_change:.8f})")
                                     if abs(price_change) < 1e-8:
                                         log.error(f"   ❌ PRICE NOT CHANGING! This is the problem!")
                                     else:
                                         log.error(f"   ✅ Price changed but state didn't - check 1s injection logic!")

                             else:
                                 log.info(f"✅ STATE CHANGED: Step {i} has different state from previous step")
                                 if hasattr(run_backtest_original, '_last_price'):
                                     price_change = current_price_close - run_backtest_original._last_price
                                     log.info(f"   Price change: {run_backtest_original._last_price:.8f} → {current_price_close:.8f} (Δ={price_change:.8f})")

                         run_backtest_original._last_state_hash = state_hash
                         run_backtest_original._last_price = current_price_close
                     
                     # ●●●●●●●● FULLY DETERMINISTIC SEEDING FOR REPRODUCIBLE SIMULATIONS ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                     # For simulate_trading: MUST be deterministic - same data = same results
                     # Use ONLY deterministic components that don't vary between Python sessions

                     # CRITICAL FIX: Use ONLY time-dependent components, NOT step index
                     # This ensures same seed for same time point regardless of backtest length
                     time_component = int(current_time.timestamp()) % 10000  # Deterministic time component
                     microsecond_component = int(current_time.timestamp() * 1000000) % 10000  # Fine-grained time component
                     state_sum = float(state_clipped.sum())
                     state_component = int(abs(state_sum * 1000)) % 10000  # Deterministic state component
                     position_component = position % 100  # Position component

                     # REMOVED: Per-step RNG seeding - using global seed only
                     # Let the model use its natural stochastic behavior without per-step reseeding

                     # FORCE deterministic behavior for reproducible results
                     torch.backends.cudnn.deterministic = True
                     torch.backends.cudnn.benchmark = False

                     # ●●●●●●●● NATURAL MODEL BEHAVIOR (no per-step seeding) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                     # Using global seed only - let model use its natural stochastic behavior

                     # ●●●●●●●● CRITICAL STATE VECTOR DEBUGGING ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                     # Log detailed state vector analysis to identify why agent gives identical predictions
                     log.info(f"🔬 STATE VECTOR ANALYSIS @ {current_time}:")
                     log.info(f"   Raw state shape: {state_vector.shape}")
                     log.info(f"   Raw state mean: {np.mean(state_vector):.8f}")
                     log.info(f"   Raw state std: {np.std(state_vector):.8f}")
                     log.info(f"   Raw state hash: {hash(state_vector.tobytes())}")

                     # Log normalized state
                     log.info(f"   Normalized state shape: {state_clipped.shape}")
                     log.info(f"   Normalized state mean: {np.mean(state_clipped):.8f}")
                     log.info(f"   Normalized state std: {np.std(state_clipped):.8f}")
                     log.info(f"   Normalized state hash: {hash(state_clipped.tobytes())}")

                     # Log key indicators from current state
                     if use_1s_decisions:
                         # Get current 5m indicators
                         mask_5m_debug = data_5m.index <= current_time
                         available_5m_debug = data_5m[mask_5m_debug]
                         if not available_5m_debug.empty:
                             latest_indicators = available_5m_debug.iloc[-1]
                             log.info(f"   Current indicators in state:")
                             log.info(f"     RSI_14: {latest_indicators.get('RSI_14', 'N/A'):.6f}")
                             log.info(f"     EMA_21: {latest_indicators.get('EMA_21', 'N/A'):.6f}")
                             log.info(f"     close: {latest_indicators.get('close', 'N/A'):.6f}")
                             log.info(f"     ATR_14: {latest_indicators.get('ATR_14', 'N/A'):.6f}")

                     # Log first and last elements of state vector
                     state_flat = state_clipped.flatten()
                     log.info(f"   State vector elements:")
                     log.info(f"     First 5: {state_flat[:5]}")
                     log.info(f"     Last 5: {state_flat[-5:]}")
                     log.info(f"     Elements 100-105: {state_flat[100:105] if len(state_flat) > 105 else 'N/A'}")

                     # Check if VecNormalize is causing issues
                     if vecnorm is not None:
                         log.info(f"   VecNormalize active: YES")
                         log.info(f"   VecNormalize mean shape: {vecnorm.obs_rms.mean.shape if hasattr(vecnorm.obs_rms, 'mean') else 'N/A'}")
                         log.info(f"   VecNormalize var shape: {vecnorm.obs_rms.var.shape if hasattr(vecnorm.obs_rms, 'var') else 'N/A'}")
                     else:
                         log.info(f"   VecNormalize active: NO")

                     # ●●●●●●●● CONTROLLED STOCHASTIC PREDICTIONS FOR REALISTIC VARIABILITY ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                     # Use stochastic predictions with controlled seeding for reproducible but varied results
                     action_raw1, _ = agent.predict(state_clipped, deterministic=False)

                     # ●●●●●●●● UPDATE THRESHOLD EVOLUTION WITH ACTUAL ENTRY SIGNAL ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                     # Update the entry signal in threshold evolution data for this step
                     if isinstance(action_raw1, np.ndarray) and action_raw1.size >= 1:
                         current_entry_sig = float(np.clip(action_raw1.flatten()[0], -1.0, 1.0))
                         # Update the last entry in threshold_evolution (current step)
                         if len(threshold_evolution['entry_signals']) > 0:
                             threshold_evolution['entry_signals'][-1] = current_entry_sig

                     # Extended debugging for constant signal issue
                     if i <= 20:  # Extended debugging
                         action_raw2, _ = agent.predict(state_clipped, deterministic=False)
                         action_raw3, _ = agent.predict(state_clipped, deterministic=False)

                         # Log detailed prediction analysis
                         log.info(f"🔍 PREDICTION DEBUG @ {current_time} (step {i}):")
                         log.info(f"   Pred1: {action_raw1}")
                         log.info(f"   Pred2: {action_raw2}")
                         log.info(f"   Pred3: {action_raw3}")

                         # Check if predictions are identical (they should be with deterministic=False)
                         pred1_entry = action_raw1[0] if len(action_raw1) > 0 else 0.0
                         pred2_entry = action_raw2[0] if len(action_raw2) > 0 else 0.0
                         pred3_entry = action_raw3[0] if len(action_raw3) > 0 else 0.0

                         variance = np.var([pred1_entry, pred2_entry, pred3_entry])
                         log.info(f"   Entry signals: [{pred1_entry:.8f}, {pred2_entry:.8f}, {pred3_entry:.8f}]")
                         log.info(f"   Variance: {variance:.12f} (should be ~0 for deterministic)")

                         # Track prediction changes between steps
                         if hasattr(run_backtest_original, '_last_entry_sig'):
                             sig_change = pred1_entry - run_backtest_original._last_entry_sig
                             log.info(f"   Signal change from last step: {run_backtest_original._last_entry_sig:.8f} → {pred1_entry:.8f} (Δ={sig_change:.8f})")
                             if abs(sig_change) < 1e-8:
                                 log.error(f"   ❌ ENTRY SIGNAL NOT CHANGING! This confirms the constant signal problem!")
                             else:
                                 log.info(f"   ✅ Entry signal changed between steps")

                         # ●●●●●●●● CROSS-DATE PREDICTION TRACKING ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                         # Track if we're getting identical predictions across different dates
                         prediction_key = f"step_{i}_sig_{pred1_entry:.6f}"
                         if not hasattr(run_backtest_original, '_prediction_history'):
                             run_backtest_original._prediction_history = {}

                         if prediction_key in run_backtest_original._prediction_history:
                             previous_date = run_backtest_original._prediction_history[prediction_key]
                             log.error(f"   🚨 IDENTICAL PREDICTION DETECTED!")
                             log.error(f"   🚨 Same signal {pred1_entry:.6f} at step {i} was seen on {previous_date}")
                             log.error(f"   🚨 Current date: {current_time.date()}")
                             log.error(f"   🚨 This indicates agent is NOT using current indicators!")
                         else:
                             run_backtest_original._prediction_history[prediction_key] = current_time.date()

                         run_backtest_original._last_entry_sig = pred1_entry
                     else:
                         action_raw2 = action_raw3 = action_raw1  # Šetrenie času
                     
                     action_raw = action_raw1  # Use first prediction
                     if not isinstance(action_raw, np.ndarray) or action_raw.size < 4: log.error(f"Predict vrátil nečakaný tvar/veľkosť: {action_raw}. Skip."); continue
                     action_clipped = np.clip(action_raw.flatten(), -1.0, 1.0)
                     raw_entry_sig = action_clipped[0]; a_tp = action_clipped[2]; exit_sig = action_clipped[3] # a_sl = action_clipped[1] - nepoužívame

                     # ●●●●●●●● ADVANCED ADAPTIVE SIGNAL SMOOTHING ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                     # Calculate ATR ratio for volatility-based adjustments (always needed)
                     atr_ratio = 1.0
                     if current_atr > 0:
                         # Calculate average ATR from recent history (if available)
                         if len(atr_history) >= 20:
                             avg_atr = np.mean(atr_history[-20:])
                             if avg_atr > 0:
                                 atr_ratio = current_atr / avg_atr
                         else:
                             atr_ratio = 1.0  # Default when not enough history

                     # Apply advanced signal processing based on configuration
                     if use_smoothed_signals:
                         # Generate multiple predictions for ensemble methods (if configured)
                         multi_predictions = None
                         if smoothing_method == "multi_pred":
                             try:
                                 # Generate multiple predictions with different random seeds
                                 multi_predictions = []
                                 for _ in range(signal_smoothing_config.get('multiPredictionCount', 7)):
                                     pred_result = agent.predict(state_clipped, deterministic=False)
                                     multi_predictions.append(pred_result[0][0])
                             except Exception as e:
                                 log.warning(f"Failed to generate multiple predictions: {e}")
                                 multi_predictions = [raw_entry_sig]

                         # Apply advanced smoothing
                         smoothing_result = signal_smoother.update(
                             raw_signal=raw_entry_sig,
                             atr_ratio=atr_ratio,
                             multi_predictions=multi_predictions
                         )

                         smoothed_entry_sig = smoothing_result['smoothed_signal']
                         signal_confidence = smoothing_result['confidence']
                         method_info = smoothing_result['method_info']

                         # Log smoothing effect for debugging (to main log for first 10 steps)
                         if i <= 10:
                             smoothing_effect = abs(smoothed_entry_sig - raw_entry_sig)
                             log.info(f"🎛️ ADVANCED SIGNAL SMOOTHING @ {current_time}:")
                             log.info(f"   Method: {smoothing_method}")
                             log.info(f"   Raw signal: {raw_entry_sig:.6f}")
                             log.info(f"   Smoothed: {smoothed_entry_sig:.6f}")
                             log.info(f"   Confidence: {signal_confidence:.3f}")
                             log.info(f"   ATR ratio: {atr_ratio:.3f}")
                             log.info(f"   Smoothing effect: {smoothing_effect:.6f}")
                             log.info(f"   Method info: {method_info}")

                         # Always log to indicators file for complete record
                         smoothing_effect = abs(smoothed_entry_sig - raw_entry_sig)
                         indicators_log.info(f"🎛️ ADVANCED SIGNAL SMOOTHING:")
                         indicators_log.info(f"   Method: {smoothing_method}")
                         indicators_log.info(f"   Raw signal: {raw_entry_sig:.6f}")
                         indicators_log.info(f"   Smoothed: {smoothed_entry_sig:.6f}")
                         indicators_log.info(f"   Confidence: {signal_confidence:.3f}")
                         indicators_log.info(f"   ATR ratio: {atr_ratio:.3f}")
                         indicators_log.info(f"   Smoothing effect: {smoothing_effect:.6f}")
                         indicators_log.info(f"   Method info: {method_info}")
                     else:
                         # Use raw signals without smoothing
                         smoothed_entry_sig = raw_entry_sig
                         signal_confidence = 1.0
                         if i <= 10:
                             log.info(f"🎛️ RAW SIGNAL MODE @ {current_time}: Using raw signal: {raw_entry_sig:.6f}")
                         indicators_log.info(f"🎛️ RAW SIGNAL MODE: Using raw signal: {raw_entry_sig:.6f}")



                     # Update signal history for stability analysis
                     signal_history.append({
                         'timestamp': current_time,
                         'raw_signal': raw_entry_sig,
                         'smoothed_signal': smoothed_entry_sig,
                         'step': i
                     })

                     # Keep only recent history (last 10 signals)
                     if len(signal_history) > 10:
                         signal_history.pop(0)

                     # ●●●●●●●● ADVANCED SIGNAL STABILITY & ADAPTIVE THRESHOLDS ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                     # Get advanced stability metrics from signal smoother
                     stability_metrics = signal_smoother.get_stability_metrics()

                     # Enhanced stability check using multiple criteria
                     signal_stable = True
                     stability_reasons = []

                     # Relaxed variance check (was 0.1, now 0.3)
                     if stability_metrics['variance'] > 0.3:
                         signal_stable = False
                         stability_reasons.append(f"high_variance({stability_metrics['variance']:.3f})")

                     # Relaxed range check (was 1.2, now 2.0)
                     if stability_metrics['range'] > 2.0:
                         signal_stable = False
                         stability_reasons.append(f"high_range({stability_metrics['range']:.3f})")

                     # Relaxed trend strength check (was 0.1, now 0.05)
                     if stability_metrics['trend_strength'] < 0.05 and len(signal_smoother.smoothed_history) >= 5:
                         signal_stable = False
                         stability_reasons.append(f"weak_trend({stability_metrics['trend_strength']:.3f})")

                     # Calculate adaptive thresholds based on signal confidence and market conditions
                     adaptive_long_thr, adaptive_short_thr, adaptive_exit_thr = threshold_tuner.calculate_adaptive_thresholds(
                         signal_confidence=signal_confidence,
                         atr_ratio=atr_ratio,
                         recent_performance=None  # TODO: Add recent performance tracking
                     )



                     if i <= 10:
                         log.info(f"🔒 ADVANCED STABILITY CHECK @ {current_time}:")
                         log.info(f"   Signal confidence: {signal_confidence:.3f}")
                         log.info(f"   Stability metrics: {stability_metrics}")
                         log.info(f"   Signal stable: {signal_stable}")
                         log.info(f"   Stability issues: {stability_reasons if not signal_stable else 'None'}")
                         log.info(f"   Adaptive thresholds: L={adaptive_long_thr:.3f}, S={adaptive_short_thr:.3f}, E={adaptive_exit_thr:.3f}")

                     # Always log to indicators file for complete record
                     indicators_log.info(f"🔒 ADVANCED STABILITY CHECK:")
                     indicators_log.info(f"   Signal confidence: {signal_confidence:.3f}")
                     indicators_log.info(f"   Stability metrics: {stability_metrics}")
                     indicators_log.info(f"   Signal stable: {signal_stable}")
                     indicators_log.info(f"   Stability issues: {stability_reasons if not signal_stable else 'None'}")
                     indicators_log.info(f"   Adaptive thresholds: L={adaptive_long_thr:.3f}, S={adaptive_short_thr:.3f}, E={adaptive_exit_thr:.3f}")

                     # Use adaptive thresholds instead of static ones
                     long_entry_thr = adaptive_long_thr
                     short_entry_thr = adaptive_short_thr
                     exit_thr = adaptive_exit_thr

                     # Use processed signal for threshold evaluation
                     entry_sig = smoothed_entry_sig

                     # Update last signal for next smoothing iteration (only if smoothing is enabled)
                     if use_smoothed_signals:
                         last_entry_signal = smoothed_entry_sig
                     else:
                         last_entry_signal = raw_entry_sig  # Keep raw for consistency

                     # Log all entry signals for analysis
                     entry_signals_log.append({
                         'timestamp': current_time,
                         'step': i,
                         'entry_sig': entry_sig,
                         'position': position,
                         'price': current_price_close
                     })
                     


                     # ●●●●●●●● ADAPTIVE THRESHOLD EXCEEDANCE LOGGING ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                     # Log threshold exceedances with adaptive thresholds
                     long_exceeded = entry_sig >= adaptive_long_thr
                     short_exceeded = entry_sig <= adaptive_short_thr
                     exit_exceeded = position != 0 and abs(exit_sig) >= adaptive_exit_thr

                     # Log to threshold exceedance file
                     threshold_tuner.log_threshold_exceedance(
                         timestamp=current_time,
                         signal_value=entry_sig,
                         threshold_type="LONG",
                         threshold_value=adaptive_long_thr,
                         exceeded=long_exceeded,
                         log_file=threshold_log_file
                     )

                     threshold_tuner.log_threshold_exceedance(
                         timestamp=current_time,
                         signal_value=entry_sig,
                         threshold_type="SHORT",
                         threshold_value=adaptive_short_thr,
                         exceeded=short_exceeded,
                         log_file=threshold_log_file
                     )

                     if position != 0:
                         threshold_tuner.log_threshold_exceedance(
                             timestamp=current_time,
                             signal_value=exit_sig,
                             threshold_type="EXIT",
                             threshold_value=adaptive_exit_thr,
                             exceeded=exit_exceeded,
                             log_file=threshold_log_file
                         )

                     # DEBUG: CONSTANT každých 50 krokov pre detailnú analýzu
                     if i <= 3:
                         # Check variance between multiple samples
                         var_entry = float(np.asarray(np.abs(action_raw1[0] - action_raw2[0])).flatten()[0])
                         var_exit = float(np.asarray(np.abs(action_raw1[3] - action_raw2[3])).flatten()[0]) if len(action_raw1) > 3 else 0.0
                         log.info(f"{current_time}: 🤖 AGENT_VARIANCE: entry={var_entry:.6f}, exit={var_exit:.6f}")
                         log.info(f"{current_time}: 🎯 MULTI_SAMPLES: a1={action_raw1[:4]}, a2={action_raw2[:4]}")
                         log.info(f"{current_time}: 📊 STATE_HASH={state_hash}, final_entry={entry_sig:.6f}")
                         log.info(f"{current_time}: 📈 STATE_SAMPLE: first_5={state_clipped[0][:5]}, last_5={state_clipped[0][-5:]}")

                except Exception as e: log.error(f"Chyba pri predict/príprave stavu v kroku {i} ({current_time}): {e}", exc_info=True); continue

            # --- Spracovanie Akcie ---
            # 1. EXIT signálom agenta (iba ak sú agent exits povolené)
            if position != 0 and agent_exits_enabled:
                 # >>> NOVÉ: Minimum time in trade (ako v ScalpingEnv)
                 time_in_trade = current_df_idx - open_idx_sim
                 min_time_required = min_time_in_trade  # Use config parameter
                 
                 # ●●●●●●●● ADAPTIVE EXIT THRESHOLD CHECK ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                 exit_sig_long_triggered = (exit_sig >= adaptive_exit_thr and position == 1)
                 exit_sig_short_triggered = (exit_sig <= -adaptive_exit_thr and position == -1)

                 # ●●●●●●●● COMPREHENSIVE ADAPTIVE EXIT THRESHOLD LOGGING ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                 pos_type = "LONG" if position == 1 else "SHORT"
                 exit_triggered = exit_sig_long_triggered or exit_sig_short_triggered

                 # Always log exit evaluation to indicators file for complete record
                 exit_log_msg = (f"ADAPTIVE EXIT CHECK ({pos_type}) @ {current_time}: "
                               f"exit_sig={exit_sig:.6f}, "
                               f"adaptive_exit_thr={adaptive_exit_thr:.6f}, "
                               f"confidence={signal_confidence:.3f}, "
                               f"triggered={exit_triggered}")
                 indicators_log.info(exit_log_msg)

                 # Log exit signal evaluation with adaptive thresholds
                 if i % 100 == 0 or exit_triggered:
                     log.info(f"🎯 ADAPTIVE EXIT CHECK ({pos_type}): exit_sig={exit_sig:.6f}, adaptive_exit_thr={adaptive_exit_thr:.6f}, confidence={signal_confidence:.3f}, triggered={exit_triggered}")

                 # Log triggered exits prominently
                 if exit_triggered:
                     exit_msg = f"🚪 AGENT EXIT TRIGGERED ({pos_type}): {exit_sig:.6f} vs {exit_thr:.6f} at {current_time}"
                     log.info(exit_msg)
                     indicators_log.info(exit_msg)
                 
                 # >>> NOVÉ: Soft TP logic (ako v ScalpingEnv)
                 soft_tp_triggered = False
                 if (exit_sig_long_triggered or exit_sig_short_triggered) and is_atr_valid_for_calc:
                     profit = (current_price_close - entry_price) * position
                     atr_threshold = 1.5 * current_atr  # Soft TP when profit > 1.5*ATR
                     if profit > atr_threshold:
                         soft_tp_triggered = True
                         log.debug(f"{current_time}: Soft TP triggered, profit: {profit:.5f} > {atr_threshold:.5f}")
                 
                 # Check minimum time constraint (unless soft TP)
                 if (exit_sig_long_triggered or exit_sig_short_triggered) and not soft_tp_triggered:
                     if time_in_trade < min_time_required:
                         log.debug(f"{current_time}: Min time hold: {time_in_trade}s < {min_time_required}s required")
                         # Don't exit yet, continue to rest of logic
                     else:
                         # Execute exit
                         sim_exit_price = current_price_close * (1 - slippage_perc * position)
                         is_profit = (sim_exit_price - entry_price) * position > 0
                         exit_reason = "TP_SIG" if is_profit else "SL_SIG"
                         
                         log.info(f"{current_time}: EXIT {'LONG' if position==1 else 'SHORT'} {position_size:.4f} @ {sim_exit_price:.5f} (Agent signal {exit_sig:.3f})")
                         pnl = position_size * (sim_exit_price - entry_price) * position
                         exit_fee = abs(position_size * sim_exit_price * fee_perc)
                         net_pnl = pnl - entry_fee - exit_fee
                         equity_total += net_pnl  # FIXED: Use net_pnl which properly accounts for both fees
                         
                         # Daily risk tracking (using original SL for accurate R-multiple)
                         original_sl_distance = abs(entry_price - original_sl_price) if 'original_sl_price' in locals() and original_sl_price > 0 else abs(entry_price - current_sl_price)
                         r_mult = net_pnl / (original_sl_distance * position_size) if original_sl_distance > 1e-9 else 0
                         daily_risk_used += r_mult
                         daily_pnl += net_pnl
                         
                         trades.append({
                             'entry_time': entry_time, 'exit_time': current_time,
                             'direction': 'Long' if position == 1 else 'Short',
                             'size': position_size, 'entry_price': entry_price, 'exit_price': sim_exit_price,
                             'pnl': net_pnl, 'exit_reason': exit_reason, 'entry_fee': entry_fee, 'exit_fee': exit_fee
                         })
                         # PnL info already logged in detailed exit statement above
                         
                         # Reset stavu
                         position = 0; position_size = 0.0; entry_price = 0.0; entry_time = None; entry_fee = 0.0
                         current_sl_price = 0.0; original_sl_price = 0.0; current_tp_price = 0.0; current_tsl_price = 0.0; peak_price_in_trade = 0.0
                         trailing_active = False; entry_atr = 0.0; open_idx_sim = -1
                         continue
                 
                 # Soft TP execution
                 elif soft_tp_triggered:
                     sim_exit_price = current_price_close * (1 - slippage_perc * position)
                     log.info(f"{current_time}: SOFT TP EXIT {'LONG' if position==1 else 'SHORT'} {position_size:.4f} @ {sim_exit_price:.5f}")
                     pnl = position_size * (sim_exit_price - entry_price) * position
                     exit_fee = abs(position_size * sim_exit_price * fee_perc)
                     net_pnl = pnl - entry_fee - exit_fee
                     equity_total += net_pnl  # FIXED: Use net_pnl which properly accounts for both fees
                     
                     # Daily risk tracking (using original SL for accurate R-multiple)
                     original_sl_distance = abs(entry_price - original_sl_price) if 'original_sl_price' in locals() and original_sl_price > 0 else abs(entry_price - current_sl_price)
                     r_mult = net_pnl / (original_sl_distance * position_size) if original_sl_distance > 1e-9 else 0
                     daily_risk_used += r_mult
                     daily_pnl += net_pnl
                     
                     trades.append({
                         'entry_time': entry_time, 'exit_time': current_time,
                         'direction': 'Long' if position == 1 else 'Short',
                         'size': position_size, 'entry_price': entry_price, 'exit_price': sim_exit_price,
                         'pnl': net_pnl, 'exit_reason': 'SOFT_TP', 'entry_fee': entry_fee, 'exit_fee': exit_fee
                     })
                     # PnL info already logged in detailed exit statement above
                     
                     # Reset stavu
                     position = 0; position_size = 0.0; entry_price = 0.0; entry_time = None; entry_fee = 0.0
                     current_sl_price = 0.0; original_sl_price = 0.0; current_tp_price = 0.0; current_tsl_price = 0.0; peak_price_in_trade = 0.0
                     trailing_active = False; entry_atr = 0.0; open_idx_sim = -1
                     continue

            # 2. ENTRY signálom agenta (iba ak sme flat a nie sme v cooldown)
            if position == 0:
                # Debug blocking conditions
                if i % 100 == 0:
                    log.info(f"{current_time}: Entry conditions - position: {position}, cooldown: {cooldown_counter}, trading_allowed: {trading_allowed}")
                
                if cooldown_counter > 0:
                    if i % 100 == 0:
                        log.info(f"{current_time}: BLOCKED - Cooldown active ({cooldown_counter}s remaining)")
                    continue
                
                if not trading_allowed:
                    if i % 100 == 0:
                        log.info(f"{current_time}: BLOCKED - Trading not allowed (daily limits)")
                    continue
                # >>> NOVÉ: Time-of-day blackout check (ako v ScalpingEnv)
                current_hour = current_time.hour
                if current_hour in DISALLOWED_HOURS:
                    # Skip entry during blackout hours
                    if i % 100 == 0:  # Log occasionally to avoid spam
                        log.info(f"{current_time}: BLOCKED - Time blackout (hour {current_hour})")
                    continue

                # >>> NOVÉ: Date-based blackout check (e.g., first day of month for XRP escrow)
                date_blocked = False
                for blackout_rule in BLACKOUT_DATES:
                    if blackout_rule == "monthday==1" and current_time.day == 1:
                        date_blocked = True
                        break
                if date_blocked:
                    if i % 100 == 0:  # Log occasionally to avoid spam
                        log.info(f"{current_time}: BLOCKED - Date blackout (day {current_time.day})")
                    continue
                
                # ●●●●●●●● SIGNAL STABILITY GATE (prevent premature triggers) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                # Only evaluate thresholds if signal is stable
                if not signal_stable:
                    if i % 100 == 0:  # Log occasionally to main log
                        log.info(f"⏳ SIGNAL UNSTABLE @ {current_time}: Waiting for stability before threshold evaluation")
                        log.info(f"   Current signal: {entry_sig:.6f}, but variance too high")

                    # Always log to indicators file for complete record (like live_trading.py)
                    indicators_log.info(f"🔒 SIGNAL NOT STABLE - Skipping trade execution")
                    indicators_log.info(f"   Entry signal: {entry_sig:.6f}")
                    indicators_log.info(f"   Signal variance too high for safe trading")
                    continue

                # ●●●●●●●● ENTRY CHECK WITH THRESHOLD LOGGING (exactly like live_trading.py) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                triggered_pos = 0

                # ●●●●●●●● COMPREHENSIVE THRESHOLD LOGGING (exactly like live_trading.py) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
                # Log ALL thresholds and signals to file, not just triggered ones
                threshold_log_msg = (f"THRESHOLDS @ {current_time}: "
                                   f"entry_sig={entry_sig:.6f}, "
                                   f"long_thr={long_entry_thr:.6f}, "
                                   f"short_thr={short_entry_thr:.6f}, "
                                   f"exit_thr={exit_thr:.6f}")

                # Always log to indicators file for complete record
                indicators_log.info(threshold_log_msg)

                # Log entry check with adaptive thresholds
                if i % 50 == 0:  # Log more frequently than every 100 steps
                    log.info(f"🎯 ADAPTIVE ENTRY CHECK: entry_sig={entry_sig:.6f}, long_thr={adaptive_long_thr:.6f}, short_thr={adaptive_short_thr:.6f}")
                    log.info(f"   Signal confidence: {signal_confidence:.3f}, ATR ratio: {atr_ratio:.3f}")

                # Entry threshold logic with adaptive thresholds and stability confirmation
                if entry_sig >= adaptive_long_thr and signal_stable:
                    triggered_pos = 1
                    entry_msg = f"🟢 ADAPTIVE LONG ENTRY TRIGGERED: {entry_sig:.6f} >= {adaptive_long_thr:.6f} at {current_time} (confidence: {signal_confidence:.3f}, stable: {signal_stable})"
                    log.info(entry_msg)
                    indicators_log.info(entry_msg)
                elif entry_sig <= adaptive_short_thr and signal_stable:
                    triggered_pos = -1
                    entry_msg = f"🔴 ADAPTIVE SHORT ENTRY TRIGGERED: {entry_sig:.6f} <= {adaptive_short_thr:.6f} at {current_time} (confidence: {signal_confidence:.3f}, stable: {signal_stable})"
                    log.info(entry_msg)
                    indicators_log.info(entry_msg)
                elif not signal_stable:
                    # Log when entry is blocked due to instability
                    if entry_sig >= adaptive_long_thr or entry_sig <= adaptive_short_thr:
                        stability_msg = f"⚠️ ENTRY BLOCKED - Signal unstable: {entry_sig:.6f}, reasons: {stability_reasons}"
                        log.info(stability_msg)
                        indicators_log.info(stability_msg)

                # NEW: Volatility filter - skip entries in extremely high volatility
                max_volatility_threshold = trade_params.get('maxVolatilityThreshold', 0.005)
                if triggered_pos != 0 and current_atr > max_volatility_threshold:
                    log.info(f"⚠️ ENTRY BLOCKED - High volatility: ATR {current_atr:.6f} > {max_volatility_threshold} at {current_time}")
                    triggered_pos = 0

                # NEW: Trend filter - check if entry is allowed based on rolling indicators
                if triggered_pos != 0:
                    trend_allowed, trend_reason = apply_trend_filter(df_features, current_df_idx, entry_sig, config)
                    if not trend_allowed:
                        log.info(f"⚠️ ENTRY BLOCKED - Trend filter: {trend_reason} at {current_time}")
                        indicators_log.info(f"🚫 TREND FILTER BLOCK: {trend_reason}")
                        triggered_pos = 0
                    else:
                        log.info(f"✅ TREND FILTER PASSED: {trend_reason} at {current_time}")
                        indicators_log.info(f"✅ TREND FILTER OK: {trend_reason}")

                if triggered_pos != 0:
                    # Record trade event for analysis
                    trade_events.append({
                        'timestamp': current_time,
                        'step': i,
                        'entry_sig': entry_sig,
                        'triggered_pos': triggered_pos,
                        'price': current_price_close,
                        'event': 'ENTRY'
                    })

                    sim_entry_price = current_price_close * (1 + slippage_perc * triggered_pos) # Aplikuj slippage

                    # --- Upravený výpočet SL a TP ---
                    # Check if ATR-based SL/TP is enabled
                    stop_loss_atr = trade_params.get('stopLossATR', 0.0)
                    take_profit_atr = trade_params.get('takeProfitATR', 0.0)

                    if tp_mode == 'atr' and stop_loss_atr > 0 and is_atr_valid_for_calc:
                        # Pure ATR-based SL
                        min_sl_dist_points = current_atr * stop_loss_atr
                        log.info(f"{current_time}: ATR-based SL: {current_atr:.6f} × {stop_loss_atr} = {min_sl_dist_points:.5f} points")
                    elif stop_loss_capital_percent is not None:
                        # New method: SL based on capital percentage
                        # Calculate SL distance so that risk = stop_loss_capital_percent of equity
                        risk_amount = equity_total * stop_loss_capital_percent
                        # For position sizing: risk_amount = position_size * sl_distance
                        # We want: sl_distance such that we risk exactly risk_amount
                        # Assume we'll use full capital for position, so position_size ≈ equity_total / entry_price
                        # Then: risk_amount = (equity_total / entry_price) * sl_distance
                        # So: sl_distance = risk_amount * entry_price / equity_total = risk_amount * entry_price / equity_total
                        min_sl_dist_points = risk_amount * sim_entry_price / equity_total
                        log.info(f"{current_time}: SL distance based on {stop_loss_capital_percent*100:.1f}% capital risk = ${risk_amount:.2f} → {min_sl_dist_points:.5f} points")
                    else:
                        # Old method: ATR and percentage distances
                        sl_dist_perc_points = max(sim_entry_price * min_sl_dist_perc, current_price_close * 0.0005)
                        sl_dist_atr_points = 0.0
                        if is_atr_valid_for_calc: sl_dist_atr_points = current_atr * min_sl_dist_atr_mult

                        if is_atr_valid_for_calc and sl_dist_atr_points > 0:
                             min_sl_dist_points = max(sl_dist_perc_points, sl_dist_atr_points)
                             log.info(f"{current_time}: SL distance based on MAX(Perc: {sl_dist_perc_points:.5f}, ATR: {sl_dist_atr_points:.5f}) = {min_sl_dist_points:.5f}")
                        else:
                             min_sl_dist_points = sl_dist_perc_points
                             log.info(f"{current_time}: Using Perc-only SL: {min_sl_dist_points:.5f} (ATR invalid: {current_atr:.5f})")
                             # Continue with percentage-based stop loss instead of blocking

                    if min_sl_dist_points <= 1e-9:
                        log.warning(f"{current_time}: BLOCKED - Zero SL distance for {'LONG' if triggered_pos==1 else 'SHORT'}. Skip.")
                        continue

                    # FIXED: Correct SL logic for LONG and SHORT positions
                    # LONG: SL below entry, TP above entry
                    # SHORT: SL above entry, TP below entry
                    sl_price = sim_entry_price - min_sl_dist_points if triggered_pos == 1 else sim_entry_price + min_sl_dist_points

                    # >>> TP calculation based on configured mode
                    if tp_mode == 'atr' and take_profit_atr > 0 and is_atr_valid_for_calc:
                        # Pure ATR-based TP
                        tp_dist_points = current_atr * take_profit_atr
                        tp_price = sim_entry_price + tp_dist_points if triggered_pos == 1 else sim_entry_price - tp_dist_points
                        log.info(f"{current_time}: ATR-based TP: {current_atr:.6f} × {take_profit_atr} = {tp_dist_points:.5f} points")
                    elif tp_mode == 'capitalPercentage':
                        # TP based on percentage of initial capital
                        target_profit = initial_equity * tp_capital_percentage
                        # We need to calculate position size first to determine TP distance
                        temp_risk_amount = equity_total * risk_per_trade
                        temp_size_in_units = temp_risk_amount / min_sl_dist_points if min_sl_dist_points > 1e-9 else 0

                        if temp_size_in_units > 0:
                            tp_dist_points = target_profit / temp_size_in_units
                            tp_price = sim_entry_price + tp_dist_points if triggered_pos == 1 else sim_entry_price - tp_dist_points
                            log.info(f"{current_time}: TP Capital Mode - Target profit: ${target_profit:.2f}, Size: {temp_size_in_units:.4f}, TP distance: {tp_dist_points:.5f}")
                        else:
                            # Fallback to rrTarget if size calculation fails
                            tp_raw = rr_target + a_tp  # a_tp ∈ [-1,1]
                            tp_raw = max(tp_raw, 1.0)  # Ensure minimum 1R targets
                            tp_raw = np.clip(tp_raw, 1.0, 4.0)  # CRITICAL: Clip between 1.0-4.0 R like in env
                            tp_dist_points = min_sl_dist_points * tp_raw
                            tp_price = sim_entry_price + tp_dist_points if triggered_pos == 1 else sim_entry_price - tp_dist_points
                            log.warning(f"{current_time}: TP Capital Mode fallback to rrTarget due to zero size")
                    else:
                        # Default rrTarget mode - EXACTLY as in ScalpingEnv
                        tp_raw = rr_target + a_tp  # a_tp ∈ [-1,1]
                        tp_raw = max(tp_raw, 1.0)  # Ensure minimum 1R targets
                        tp_raw = np.clip(tp_raw, 1.0, 4.0)  # CRITICAL: Clip between 1.0-4.0 R like in env
                        tp_dist_points = min_sl_dist_points * tp_raw
                        tp_price = sim_entry_price + tp_dist_points if triggered_pos == 1 else sim_entry_price - tp_dist_points

                    # 🚨 CRITICAL FIX: Validate and correct SL placement for realistic simulation
                    # This ensures SHORT positions always show losses on SL exits, not gains
                    original_sl = sl_price
                    if triggered_pos == 1:  # LONG position
                        if sl_price >= sim_entry_price:
                            # SL should be BELOW entry for LONG
                            sl_price = sim_entry_price - min_sl_dist_points
                            log.warning(f"{current_time}: CORRECTED LONG SL from {original_sl:.5f} to {sl_price:.5f} (below entry {sim_entry_price:.5f})")
                    elif triggered_pos == -1:  # SHORT position
                        if sl_price <= sim_entry_price:
                            # SL should be ABOVE entry for SHORT
                            sl_price = sim_entry_price + min_sl_dist_points
                            log.warning(f"{current_time}: CORRECTED SHORT SL from {original_sl:.5f} to {sl_price:.5f} (above entry {sim_entry_price:.5f})")
                    
                    # Additional validation: Ensure SL distance is reasonable
                    actual_sl_distance = abs(sl_price - sim_entry_price)
                    if actual_sl_distance < min_sl_dist_points * 0.8:  # Allow 20% tolerance
                        log.warning(f"{current_time}: SL distance too small: {actual_sl_distance:.5f} < {min_sl_dist_points:.5f}")

                    # --- Výpočet veľkosti pozície ---
                    if position_sizing_method == 'RiskPercentage':
                        risk_amount = equity_total * risk_per_trade
                        size_in_units = risk_amount / min_sl_dist_points if min_sl_dist_points > 1e-9 else 0
                    else: log.error(f"Neznámy sizing: {position_sizing_method}"); continue

                    # >>> ENHANCED: Advanced signal strength-based position sizing
                    signal_strength = abs(entry_sig)

                    # Base size multiplier based on signal strength (0.5x to 1.5x)
                    base_multiplier = 0.5 + min(signal_strength / 0.8, 1.0)  # More aggressive scaling

                    # Volatility adjustment: reduce size in high volatility
                    volatility_multiplier = 1.0
                    if current_atr > 0:
                        # Calculate volatility percentile (rough approximation)
                        if use_1s_decisions and atr_col:
                            mask_5m_atr_hist = data_5m.index <= current_time
                            available_5m_atr_hist = data_5m[mask_5m_atr_hist]
                            if len(available_5m_atr_hist) >= 100:
                                atr_percentile = (available_5m_atr_hist[atr_col].tail(100) < current_atr).mean()
                                # Reduce size if volatility is in top 20%
                                if atr_percentile > 0.8:
                                    volatility_multiplier = 0.7
                                elif atr_percentile > 0.6:
                                    volatility_multiplier = 0.85

                    # Combine multipliers
                    size_multiplier = base_multiplier * volatility_multiplier
                    size_in_units *= size_multiplier

                    # Dynamický boost based on market microstructure
                    size_boost = 1.0
                    if has_imbalance_col:
                         imb = current_row.get("depth_imbalance5", 0.0)
                         if abs(imb) > 0.3: size_boost = 1.2  # Boost pre silnú imbalance
                         elif abs(imb) > 0.5: size_boost = 1.4  # Even stronger boost
                    size_in_units *= size_boost

                    # Kontrola max veľkosti
                    max_size_by_equity = (equity_total * max_pos_perc_equity) / sim_entry_price if sim_entry_price > 0 else 0
                    if max_size_by_equity > 0: size_in_units = min(size_in_units, max_size_by_equity)

                    if size_in_units <= 1e-9: log.warning(f"{current_time}: Nulová veľkosť {'LONG' if triggered_pos==1 else 'SHORT'}. Skip."); continue

                    # Recalculate TP for capital percentage mode with final position size
                    if tp_mode == 'capitalPercentage':
                        target_profit = initial_equity * tp_capital_percentage
                        tp_dist_points = target_profit / size_in_units
                        tp_price = sim_entry_price + tp_dist_points if triggered_pos == 1 else sim_entry_price - tp_dist_points
                        log.info(f"{current_time}: TP Capital Mode FINAL - Target: ${target_profit:.2f}, Final Size: {size_in_units:.4f}, TP: {tp_price:.5f}")

                    # --- Simulácia vstupu ---
                    current_entry_fee = size_in_units * sim_entry_price * fee_perc
                    if equity_total - current_entry_fee <= 0: log.warning(f"{current_time}: Nedostatok EQ ({equity_total:.2f}) pre poplatok ({current_entry_fee:.2f}) {'LONG' if triggered_pos==1 else 'SHORT'}."); continue

                    equity_total -= current_entry_fee
                    position = triggered_pos; position_size = size_in_units; entry_price = sim_entry_price;
                    current_sl_price = sl_price; original_sl_price = sl_price; current_tp_price = tp_price; current_tsl_price = 0.0; peak_price_in_trade = entry_price;
                    entry_time = current_time; entry_fee = current_entry_fee;
                    open_idx_sim = current_df_idx # <<<<<<<<<<<<<<<<<<<<<<< ULOŽÍME INDEX OTVORENIA
                    entry_atr = current_atr  # Store ATR at entry for TSL calculations
                    trailing_active = False  # Reset TSL state
                    
                    # >>> NOVÉ: Increment daily trade count (ako v ScalpingEnv)
                    daily_trade_count += 1

                    log.info(f"{current_time}: ENTER {'LONG' if position==1 else 'SHORT'} {position_size:.4f} @ {entry_price:.5f}, SL: {sl_price:.5f}, TP: {tp_price:.5f}, Fee: {entry_fee:.4f}, EQ: ${equity_total:.2f}")


    # --- Koniec slučky - uzavretie poslednej pozície ---
    if position != 0:
        if use_1s_decisions:
            last_time = data_1s.index[-1]
            last_price = data_1s['close'].iloc[-1]
            log.warning(f"{last_time}: Vynútené uzavretie pozície na konci (1s mode).")
        else:
            last_time = data_5m.index[-1]
            last_price = data_5m['close'].iloc[-1]
            log.warning(f"{last_time}: Vynútené uzavretie pozície na konci (5m mode).")
        exit_price = last_price * (1 - slippage_perc * position)
        pnl = position_size * (exit_price - entry_price) * position
        exit_fee = abs(position_size * exit_price * fee_perc)
        net_pnl = pnl - entry_fee - exit_fee
        equity_total += net_pnl  # FIXED: Use net_pnl which properly accounts for both fees
        
        # Daily risk tracking (using original SL for accurate R-multiple)
        original_sl_distance = abs(entry_price - original_sl_price) if 'original_sl_price' in locals() and original_sl_price > 0 else abs(entry_price - current_sl_price)
        r_mult = net_pnl / (original_sl_distance * position_size) if original_sl_distance > 1e-9 else 0
        daily_risk_used += r_mult
        daily_pnl += net_pnl
        
        trades.append({
            'entry_time': entry_time, 'exit_time': last_time,
            'direction': 'Long' if position == 1 else 'Short',
            'size': position_size, 'entry_price': entry_price, 'exit_price': exit_price,
            'pnl': net_pnl, 'exit_reason': 'End of Data', 'entry_fee': entry_fee, 'exit_fee': exit_fee
        })
        log.info(f" Uzavretá pozícia: {position_size:.4f} u {'L' if position == 1 else 'S'} @ {exit_price:.5f}")
        # PnL info already logged in detailed exit statement above

    log.info(f" Finálna Equity: ${equity_total:.2f}")
    log.info("Backtest dokončený.")

    # Analyze entry signals around trades
    if trade_events and entry_signals_log:
        log.info("=" * 60)
        log.info("📊 ENTRY SIGNALS ANALYSIS")
        log.info("=" * 60)

        for trade_event in trade_events:
            trade_step = trade_event['step']
            trade_time = trade_event['timestamp']
            trade_entry_sig = trade_event['entry_sig']

            log.info(f"\n🎯 TRADE at {trade_time} (step {trade_step})")
            log.info(f"   Entry signal: {trade_entry_sig:.6f}, Direction: {trade_event['triggered_pos']}")

            # Get 20 signals before trade
            before_signals = [s for s in entry_signals_log if s['step'] < trade_step and s['step'] >= trade_step - 20]
            before_signals = sorted(before_signals, key=lambda x: x['step'])[-20:]  # Last 20

            # Get 20 signals after trade
            after_signals = [s for s in entry_signals_log if s['step'] > trade_step and s['step'] <= trade_step + 20]
            after_signals = sorted(after_signals, key=lambda x: x['step'])[:20]  # First 20

            if before_signals:
                before_values = [s['entry_sig'] for s in before_signals]
                before_mean = np.mean(before_values)
                before_std = np.std(before_values)
                before_min = np.min(before_values)
                before_max = np.max(before_values)

                log.info(f"📉 20 signals BEFORE trade:")
                log.info(f"   Mean: {before_mean:.6f}, Std: {before_std:.6f}")
                log.info(f"   Range: [{before_min:.6f}, {before_max:.6f}]")
                log.info(f"   Values: {[f'{v:.4f}' for v in before_values[-10:]]}")  # Last 10

            if after_signals:
                after_values = [s['entry_sig'] for s in after_signals]
                after_mean = np.mean(after_values)
                after_std = np.std(after_values)
                after_min = np.min(after_values)
                after_max = np.max(after_values)

                log.info(f"📈 20 signals AFTER trade:")
                log.info(f"   Mean: {after_mean:.6f}, Std: {after_std:.6f}")
                log.info(f"   Range: [{after_min:.6f}, {after_max:.6f}]")
                log.info(f"   Values: {[f'{v:.4f}' for v in after_values[:10]]}")  # First 10

        # Overall statistics
        all_entry_sigs = [s['entry_sig'] for s in entry_signals_log]
        overall_mean = np.mean(all_entry_sigs)
        overall_std = np.std(all_entry_sigs)
        overall_min = np.min(all_entry_sigs)
        overall_max = np.max(all_entry_sigs)

        log.info(f"\n📊 OVERALL ENTRY SIGNALS STATISTICS:")
        log.info(f"   Total signals: {len(all_entry_sigs)}")
        log.info(f"   Mean: {overall_mean:.6f}, Std: {overall_std:.6f}")
        log.info(f"   Range: [{overall_min:.6f}, {overall_max:.6f}]")
        log.info(f"   Threshold: Long > {long_entry_thr}, Short < {short_entry_thr}")

        # Count signals above/below thresholds
        above_long_thresh = sum(1 for s in all_entry_sigs if s > long_entry_thr)
        below_short_thresh = sum(1 for s in all_entry_sigs if s < short_entry_thr)

        log.info(f"   Signals above long threshold ({long_entry_thr}): {above_long_thresh}")
        log.info(f"   Signals below short threshold ({short_entry_thr}): {below_short_thresh}")
        log.info(f"   Total threshold breaches: {above_long_thresh + below_short_thresh}")

        log.info("=" * 60)

        # NEW: Daily threshold exceedance summary
        from collections import defaultdict
        daily_exceedances = defaultdict(lambda: {'long': 0, 'short': 0, 'exit': 0})
        for sig in entry_signals_log:
            day = sig['timestamp'].date()
            if sig['entry_sig'] > long_entry_thr:
                daily_exceedances[day]['long'] += 1
            if sig['entry_sig'] < short_entry_thr:
                daily_exceedances[day]['short'] += 1
            # For exit, we'd need exit_sig logged similarly; assuming it's in scope

        log.info("\n📅 DAILY THRESHOLD EXCEEDANCES:")
        for day, counts in sorted(daily_exceedances.items()):
            log.info(f"{day}: Long: {counts['long']}, Short: {counts['short']}")

        log.info("=" * 60)

    # Handle empty equity_curve case
    if equity_curve:
        equity_curve_df = pd.DataFrame(equity_curve).set_index('timestamp')
    else:
        # Create empty DataFrame with proper structure
        equity_curve_df = pd.DataFrame(columns=['equity'])
        equity_curve_df.index.name = 'timestamp'
        log.warning("Equity curve is empty - no steps were executed in the simulation")

    return trades, equity_curve_df, equity_total, threshold_evolution

def calculate_metrics(trades_df: pd.DataFrame, equity_curve: pd.DataFrame, initial_equity: float, final_equity: float, start_time, end_time):
    """Vypočíta základné metriky výkonnosti."""
    metrics = {}; metrics["Obdobie"] = f"{start_time.date()} - {end_time.date()}"; metrics["Počiatočný kapitál"] = initial_equity; metrics["Konečný kapitál"] = final_equity
    metrics["Celkový PnL ($)"] = final_equity - initial_equity; metrics["Celkový PnL (%)"] = (final_equity / initial_equity - 1) * 100 if initial_equity > 1e-9 else 0

    if trades_df.empty:
        log.warning("Neboli vykonané žiadne obchody."); metrics["Počet obchodov"] = 0; [metrics.update({k: 0}) for k in ["Počet ziskových obchodov", "Počet stratových obchodov", "Win Rate (%)", "Priemerný zisk ($)", "Priemerná strata ($)", "Priemerný PnL na obchod ($)", "Najväčší zisk ($)", "Najväčšia strata ($)", "Celkové poplatky ($)", "Profit Factor", "Risk/Reward Ratio (Avg)", "Maximálny Drawdown ($)", "Maximálny Drawdown (%)"]]; [metrics.update({k: "N/A"}) for k in ["Ročný výnos (%)", "Calmar Ratio (approx)"]]; return metrics

    metrics["Počet obchodov"] = len(trades_df); wins = trades_df[trades_df['pnl'] > 0]; losses = trades_df[trades_df['pnl'] <= 0]
    metrics["Počet ziskových obchodov"] = len(wins); metrics["Počet stratových obchodov"] = len(losses); metrics["Win Rate (%)"] = (len(wins) / len(trades_df)) * 100 if len(trades_df) > 0 else 0
    metrics["Priemerný zisk ($)"] = wins['pnl'].mean() if len(wins) > 0 else 0; metrics["Priemerná strata ($)"] = losses['pnl'].mean() if len(losses) > 0 else 0
    avg_loss_abs = abs(metrics["Priemerná strata ($)"]) if len(losses) > 0 else 0; metrics["Priemerný PnL na obchod ($)"] = trades_df['pnl'].mean()
    metrics["Najväčší zisk ($)"] = trades_df['pnl'].max(); metrics["Najväčšia strata ($)"] = trades_df['pnl'].min()
    total_fees = trades_df['entry_fee'].sum() + trades_df['exit_fee'].sum(); metrics["Celkové poplatky ($)"] = total_fees
    sum_wins = wins['pnl'].sum(); sum_losses = abs(losses['pnl'].sum())
    metrics["Profit Factor"] = (sum_wins / sum_losses) if sum_losses > 1e-9 else (np.inf if sum_wins > 1e-9 else 0)
    metrics["Risk/Reward Ratio (Avg)"] = (abs(metrics["Priemerný zisk ($)"]) / avg_loss_abs) if avg_loss_abs > 1e-9 else (np.inf if abs(metrics["Priemerný zisk ($)"]) > 1e-9 else 0)

    if not equity_curve.empty:
        equity_curve['peak'] = equity_curve['equity'].cummax(); equity_curve['drawdown'] = equity_curve['equity'] - equity_curve['peak']
        equity_curve['drawdown_pct'] = np.where( equity_curve['peak'] > 1e-9, (equity_curve['equity'] / equity_curve['peak'] - 1) * 100, 0.0 )
        metrics["Maximálny Drawdown ($)"] = equity_curve['drawdown'].min() if not equity_curve.empty else 0
        metrics["Maximálny Drawdown (%)"] = equity_curve['drawdown_pct'].min() if not equity_curve.empty else 0
        duration_days = (end_time - start_time).total_seconds() / (24 * 3600)
        total_return = (final_equity / initial_equity) - 1 if initial_equity > 1e-9 else 0
        
        # Anualizácia má zmysel iba pre obdobia dlhšie ako 30 dní
        MINIMUM_DAYS_FOR_ANNUALIZATION = 30
        
        if duration_days >= MINIMUM_DAYS_FOR_ANNUALIZATION:
            duration_years = duration_days / 365.25
            if duration_years > 0 and total_return > -1:
                annualized_return = (1 + total_return)**(1 / duration_years) - 1
                metrics["Ročný výnos (%)"] = annualized_return * 100
                max_drawdown_abs_pct = abs(metrics["Maximálny Drawdown (%)"])
                metrics["Calmar Ratio (approx)"] = ((annualized_return * 100) / max_drawdown_abs_pct) if max_drawdown_abs_pct > 1e-9 else (np.inf if annualized_return > 1e-9 else 0)
            else:
                log.warning("Neplatné hodnoty pre anualizáciu (duration_years <= 0 alebo total_return <= -1)")
                metrics["Ročný výnos (%)"] = "N/A"
                metrics["Calmar Ratio (approx)"] = "N/A"
        else:
            log.info(f"Trvanie backtestu ({duration_days:.1f} dní) je príliš krátke pre anualizáciu (min. {MINIMUM_DAYS_FOR_ANNUALIZATION} dní)")
            # Pre krátke obdobia poskytujeme alternatívne metriky
            daily_return = total_return / duration_days if duration_days > 0 else 0
            weekly_return = daily_return * 7
            monthly_return = daily_return * 30
            
            metrics["Denný výnos (%)"] = daily_return * 100
            metrics["Týždenný výnos (%) odhad"] = weekly_return * 100
            metrics["Mesačný výnos (%) odhad"] = monthly_return * 100
            metrics["Ročný výnos (%)"] = "N/A (krátke obdobie)"
            metrics["Calmar Ratio (approx)"] = "N/A (krátke obdobie)"
    else: log.warning("Equity krivka prázdna."); [metrics.update({k: 0}) for k in ["Maximálny Drawdown ($)", "Maximálny Drawdown (%)"]]; [metrics.update({k: "N/A"}) for k in ["Ročný výnos (%)", "Calmar Ratio (approx)"]];

    return metrics

# --- Hlavná časť skriptu ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Spustí backtest s parameter sweep pre natrénovaného agenta.")
    parser.add_argument("--cfg", required=True, type=Path, help="Cesta ku konfiguračnému súboru (YAML/JSON).")
    parser.add_argument("--start", required=True, help="Dátum začiatku backtestu (YYYY-MM-DD).")
    parser.add_argument("--end", required=True, help="Dátum konca backtestu (YYYY-MM-DD, vrátane).")
    parser.add_argument("--model", type=Path, default=None, help="Voliteľná cesta k modelu agenta (prepíše cestu v configu).")
    parser.add_argument("--out-trades", type=Path, default="backtest_trades.csv", help="Súbor pre uloženie zoznamu obchodov.")
    parser.add_argument("--out-equity", type=Path, default="backtest_equity.csv", help="Súbor pre uloženie krivky kapitálu.")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"], help="Úroveň logovania.")
    parser.add_argument("--use-1s-decisions", action="store_true", help="Agent robí rozhodnutia každú sekundu namiesto každých 5 minút (experimentálne).")
    parser.add_argument("--enable-parameter-sweep", action="store_true", help="Aktivuje parameter sweep testovanie.")
    args = parser.parse_args()

    # --- Nastavenie Loggera ---
    # Clear all existing handlers to prevent duplication
    for handler in log.handlers[:]:
        log.removeHandler(handler)
    
    # Prevent propagation to root logger to avoid duplicate messages
    log.propagate = False
    
    log.setLevel(args.log_level.upper())
    handler = logging.StreamHandler()
    
    # Clean, professional formatter
    formatter = logging.Formatter("%(asctime)s [%(levelname)s] %(message)s", datefmt="%Y-%m-%d %H:%M:%S")
    handler.setFormatter(formatter)
    log.addHandler(handler)
    log.info(f"Logging nastavený na úroveň: {args.log_level.upper()}")
    log.info(f"Použitá knižnica pre gym.spaces: {log_gym_lib}")


    try:
        config = load_config(args.cfg)
        
        # Automatická detekcia model a vecnorm súborov
        if args.model:
            model_path = Path(args.model)
        else:
            model_path = config.get('runtime', {}).get('model_path')
            if not model_path:
                raise ValueError("Cesta k modelu nie je definovaná ani v args ani v configu.")
            model_path = Path(model_path)
        
        # Ak model_path neexistuje ako .zip, skúsme nájsť checkpoint súbory
        if not model_path.exists():
            # Skúsme nájsť posledný checkpoint v chk/ adresári
            chk_dir = Path("./chk")
            if chk_dir.exists():
                # Nájdeme všetky .zip súbory v chk/
                model_files = list(chk_dir.glob("sac_*_steps.zip"))
                if model_files:
                    # Sortujeme podľa timesteps a vezmeme posledný
                    model_files.sort(key=lambda x: int(x.stem.split('_')[1]))
                    model_path = model_files[-1]
                    log.info(f"Auto-detekovaný model checkpoint: {model_path}")
        
        if not model_path.exists():
            log.error(f"Model neexistuje: {model_path}")
            exit(1)
            
        # Nájdenie zodpovedajúceho VecNormalize súboru
        vecnorm_path = model_path.with_suffix('.vecnorm.pkl')
        if not vecnorm_path.exists():
            # Skúsme alternatívny formát názvu
            model_stem = model_path.stem.replace('_steps', '')
            vecnorm_path = model_path.parent / f"{model_stem}.vecnorm.pkl"
            
        if not vecnorm_path.exists():
            log.warning(f"VecNormalize súbor nenájdený: {vecnorm_path}. Pozorování nebudú normalizované!")
            vecnorm = None
        else:
            log.info(f"Načítavam VecNormalize: {vecnorm_path}")
            try:
                from stable_baselines3.common.vec_env import DummyVecEnv
                import gymnasium as gym
                
                # Vytvoríme dummy environment pre načítanie VecNormalize
                class DummyEnv(gym.Env):
                    def __init__(self):
                        super().__init__()
                        self.observation_space = gym.spaces.Box(-5.0, 5.0, (1451,), dtype=np.float32)
                        self.action_space = gym.spaces.Box(-1.0, 1.0, (4,), dtype=np.float32)
                    
                    def reset(self, **kwargs):
                        return np.zeros(1451, dtype=np.float32), {}
                    
                    def step(self, action):
                        return np.zeros(1451, dtype=np.float32), 0.0, False, False, {}
                
                dummy_env = DummyVecEnv([lambda: DummyEnv()])
                vecnorm = VecNormalize.load(vecnorm_path, dummy_env)
                vecnorm.training = False  # Nastavíme na evaluation mode
                log.info("VecNormalize úspešne načítané")
            except Exception as e:
                log.error(f"Chyba pri načítavaní VecNormalize: {e}")
                vecnorm = None
        
        log.info(f"Používam model: {model_path}")
        
        # ===== PATCH PRE POPLARTSAC LOADING =====
        # Importy pre patch
        import json
        from agent import SimpleCNN1D, SafeReplayBuffer
        from popart_sac import PopArtSAC

        # Načítanie konfigurácie pre feature extraction z config objektu
        feats = config["envSettings"]["feature_columns"]
        training_settings = config["trainingSettings"]
        
        # Nastavenie policy_kwargs s CNN feature extractorom z configu
        policy_kwargs = {
            "net_arch": training_settings["netArch"],
            "features_extractor_class": SimpleCNN1D,
            "features_extractor_kwargs": {
                **training_settings["featureExtractorKwargs"],
                "feature_names": feats,
                "meta_len": 11
            }
        }

        # Aktualizácia custom_objects s proper replay buffer a learning rate
        custom_objects.update({
            "buffer_size": training_settings["bufferSize"],
            "policy_kwargs": policy_kwargs,
            "replay_buffer_class": SafeReplayBuffer,
            "learning_rate": 0.0001
        })
        # ===== KONIEC PATCH =====

        try:
            agent = PopArtSAC.load(model_path, device="cpu", custom_objects=custom_objects)
            log.info(f"Agent typu {type(agent).__name__} načítaný cez PopArtSAC.")
        except Exception as e:
            log.error(f"Chyba pri načítavaní modelu cez PopArtSAC: {e}", exc_info=True)
            exit(1)

        start_dt = datetime.strptime(args.start, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        end_dt = datetime.strptime(args.end, "%Y-%m-%d").replace(hour=23, minute=59, second=59, microsecond=999999, tzinfo=timezone.utc)
        
        # Load data with 1s decision support - check both config and command line
        use_1s_decisions = config.get('use_1s_decisions', False) or args.use_1s_decisions
        backtest_data = load_backtest_data(config, start_dt, end_dt,
                                          load_second_data=True,
                                          use_1s_decisions=use_1s_decisions)

        # --- Skontrolujeme, či máme dosť dát po načítaní ---
        if len(backtest_data['primary']) < config['envSettings']['state_lookback']:
            log.error(f"Nedostatok dát ({len(backtest_data['primary'])} riadkov) pre spustenie backtestu s lookbackom {config['envSettings']['state_lookback']}. Končím.")
            exit(1)

        # Rozhodnutie o type backtestu
        if args.enable_parameter_sweep:
            log.info("🧪 PARAMETER SWEEP MODE ACTIVATED")
            trades_list, equity_curve_df, final_equity, sweep_results = run_backtest_with_parameter_sweep(
                config, backtest_data, agent, vecnorm, use_1s_decisions=use_1s_decisions
            )
        else:
            log.info("📊 STANDARD BACKTEST MODE")
            trades_list, equity_curve_df, final_equity, threshold_evolution = run_backtest_original(
                config, backtest_data, agent, vecnorm, use_1s_decisions=use_1s_decisions
            )
            
        trades_df = pd.DataFrame(trades_list)

        log.info("=" * 30 + " VÝSLEDKY BACKTESTU " + "=" * 30)
        initial_equity = config.get('account', {}).get('initialEquity', 10000)
        # Získanie časov z dát, ak krivka neexistuje (napr. žiadne kroky)
        start_time = equity_curve_df.index.min() if not equity_curve_df.empty else (backtest_data['primary'].index.min() if not backtest_data['primary'].empty else start_dt)
        end_time = equity_curve_df.index.max() if not equity_curve_df.empty else (backtest_data['primary'].index.max() if not backtest_data['primary'].empty else end_dt)
        metrics = calculate_metrics(trades_df, equity_curve_df, initial_equity, final_equity, start_time, end_time)

        print("\n" + "=" * 30 + " VÝSLEDKY BACKTESTU " + "=" * 30)
        # Vylepšené formátovanie metrík
        for key, value in metrics.items():
             # Zistíme typ a podľa toho formátujeme
            if isinstance(value, float) and not np.isinf(value) and pd.notna(value):
                # Použijeme .4f pre % a .2f pre $
                if '%' in key or 'Ratio' in key or 'Factor' in key:
                    print(f"{key:<30}: {value:>15.4f}")
                else:
                    print(f"{key:<30}: {value:>15.2f}")
            elif isinstance(value, int):
                print(f"{key:<30}: {value:>15d}")
            else: # Pre stringy ako "N/A" alebo dátumy
                 print(f"{key:<30}: {str(value):>15}")
        print("=" * (60 + len(" VÝSLEDKY BACKTESTU ")) + "\n")

        # === VYMAZANIE STARÝCH SÚBOROV VÝSLEDKOV ===
        # Vymazanie starých súborov pred uložením nových výsledkov, aby sa predišlo zmätku
        if args.out_trades and Path(args.out_trades).exists():
            try:
                Path(args.out_trades).unlink()
                log.info(f"Vymazaný starý súbor obchodov: {args.out_trades}")
            except Exception as e:
                log.warning(f"Nepodarilo sa vymazať starý súbor obchodov {args.out_trades}: {e}")

        if args.out_equity and Path(args.out_equity).exists():
            try:
                Path(args.out_equity).unlink()
                log.info(f"Vymazaný starý súbor equity: {args.out_equity}")
            except Exception as e:
                log.warning(f"Nepodarilo sa vymazať starý súbor equity {args.out_equity}: {e}")

        # === ULOŽENIE NOVÝCH VÝSLEDKOV ===
        if args.out_trades:
            try:
                if not trades_df.empty: trades_df.to_csv(args.out_trades, index=False, float_format='%.8f'); log.info(f"Obchody uložené do: {args.out_trades}")
                else: log.info(f"Žiadne obchody, súbor {args.out_trades} nebol vytvorený.")
            except Exception as e: log.error(f"Chyba pri ukladaní obchodov do {args.out_trades}: {e}")
        if args.out_equity:
             try:
                if not equity_curve_df.empty: equity_curve_df.to_csv(args.out_equity, float_format='%.4f'); log.info(f"Equity krivka uložená do: {args.out_equity}")
                else: log.info(f"Equity krivka prázdna, súbor {args.out_equity} nebol vytvorený.")
             except Exception as e: log.error(f"Chyba pri ukladaní equity krivky do {args.out_equity}: {e}")

        # === THRESHOLD EVOLUTION GRAPH GENERATION ===
        try:
            log.info("📊 Generating threshold evolution graph...")
            if threshold_evolution and threshold_evolution.get('timestamps'):
                generate_threshold_evolution_graph(threshold_evolution, config['symbol'], args.start, args.end)
            else:
                log.warning("⚠️ No threshold evolution data available for graph generation")
        except Exception as e:
            log.error(f"❌ Error generating threshold evolution graph: {e}")

        # === AUTOMATICKÁ ANALÝZA VÝSLEDKOV ===
        try:
            log.info("🔍 Spúšťam automatickú analýzu výsledkov...")
            from analyze_simulation_results import analyze_simulation_results
            
            # Vytvorenie timestampovaného adresára pre analýzu
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            analysis_dir = f"simulation_analysis_{timestamp}"
            
            analyze_simulation_results(
                trades_file=str(args.out_trades) if args.out_trades else "backtest_trades.csv",
                equity_file=str(args.out_equity) if args.out_equity else "backtest_equity.csv",
                output_dir=analysis_dir
            )
            log.info(f"✅ Analýza dokončená! Výsledky v adresári: {analysis_dir}")
            
        except ImportError:
            log.warning("⚠️ analyze_simulation_results.py nenájdený, preskakujem automatickú analýzu")
        except Exception as e:
            log.error(f"❌ Chyba pri automatickej analýze: {e}")

    except FileNotFoundError as e: log.error(f"Chyba: Súbor nenájdený: {e}"); exit(1)
    except (ValueError, TypeError, KeyError) as e: log.error(f"Chyba v dátach/konfigurácii: {e}", exc_info=True); exit(1)
    except Exception as e: log.error(f"Neočekávaná chyba počas backtestu:", exc_info=True); exit(1)